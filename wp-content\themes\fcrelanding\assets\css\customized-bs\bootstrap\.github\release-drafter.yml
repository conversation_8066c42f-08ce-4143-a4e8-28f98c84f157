name-template: 'v$NEXT_MAJOR_VERSION'
tag-template: 'v$NEXT_MAJOR_VERSION'
prerelease: true
exclude-labels:
  - 'skip-changelog'
categories:
  - title: '❗ Breaking Changes'
    labels:
      - 'breaking-change'
  - title: '🚀 Features'
    labels:
      - 'new-feature'
      - 'feature'
      - 'enhancement'
  - title: '🐛 Bug fixes'
    labels:
      - 'fix'
      - 'bugfix'
      - 'bug'
  - title: '⚡ Performance improvements'
    labels:
      - 'performance'
  - title: '🎨 CSS'
    labels:
      - 'css'
  - title: '☕️ JavaScript'
    labels:
      - 'js'
  - title: '📖 Docs'
    labels:
      - 'docs'
  - title: '🛠 Examples'
    labels:
      - 'examples'
  - title: '🌎 Accessibility'
    labels:
      - 'accessibility'
  - title: '🔧 Utility API'
    labels:
      - 'utility API'
      - 'utilities'
  - title: '🏭 Tests'
    labels:
      - 'tests'
  - title: '🧰 Misc'
    labels:
      - 'build'
      - 'meta'
      - 'chore'
      - 'CI'
  - title: '📦 Dependencies'
    labels:
      - 'dependencies'
change-template: '- #$NUMBER: $TITLE'
template: |
  ## Changes
  $CHANGES
