@charset "UTF-8";
/*
Theme Name: L.I.R.C.S Wordpress Theme
Author: <PERSON>
Author URI: https://focusedcre.com
Description: Custom wordpress theme for .I.R.C.S
Version: 1
*/

:root{
	--primary: #092a63;
    --secondary: #51cb3f;
    --overlay: #072961;
    --primaryColor: var(--primary);
    --secondaryColor: var(--secondary);
}

body{overflow-x: hidden !important; font-family: "Montserrat", sans-serif; font-optical-sizing: auto; color: #000000;
    line-height: 1.4;
}
body.darkTheme {
    --primaryColor: var(--secondary);
    --secondaryColor: var(--primary);
}
#mainWebsiteContainer {overflow-x: clip;}
/************************* fonts, buttons, icons and text blocks styles**********************************/
h1{font-size: 50px; color: var(--primaryColor); line-height: 1.2;}
h2{font-size: 44px; color: var(--primaryColor); line-height: 1.2;}
h3{font-size: 24px; color: var(--primaryColor); line-height: 1.2;}
h4{font-size: 20px; color: var(--primaryColor); line-height: 1.3;}
h5{font-size: 16px; color: var(--primaryColor); line-height: 1.4;}
h6{font-size: 14px; color: var(--primaryColor); line-height: 1.4;}

a{color: var(--primaryColor); font-weight: 500;text-decoration: none; -webkit-transition: 0.3s ease-in-out !important;-moz-transition: 0.3s ease-in-out !important;-ms-transition: 0.3s ease-in-out !important;-o-transition: 0.3s ease-in-out !important;transition: 0.3s ease-in-out !important;}
a:hover{color: #000000; text-decoration: none;}
a:focus{text-decoration: none; outline: none}
ul{margin: 0; padding: 0}
ul li{list-style: none;}
img{image-rendering: -webkit-optimize-contrast;}

#map{max-height: 100%;}

.id-scroll-fix-special {position: relative; top: -140px;}
 
.cusBtn{
	position: relative;
    padding: 20px 50px;
    font-weight: 500;
    display: inline-block;
    text-transform: uppercase;
    background: #b31926;
    color: #ffffff;
    font-size: 18px;
    border: 1px solid #b31926;
}
.cusBtn:hover{ 
	background: #ffffff;
	color: #b31926;
    border: 1px solid #ffffff;
}

.cusBtn2{
	position: relative;
    padding: 20px 50px;
    font-weight: 500;
    display: inline-block;
    text-transform: uppercase;
    background: #b31926;
    color: #ffffff;
    font-size: 18px;
    border: 1px solid #b31926;
}
.cusBtn2:hover{ 
	background: #273e63;
	color: #ffffff;
    border: 1px solid #273e63;
}


/*--------------------------------------------------------------
# Header
--------------------------------------------------------------*/

#header {
	border-bottom: 1px solid #dddddd;
    padding: 35px 50px;
    background: #ffffff;
}
#header.header-scrolled {
	background: #ffffff;
	top: 0;
    padding: 35px 50px;
}
#header .logo img {
    width: 300px;
}
 

/*--------------------------------------------------------------
# Desktop Navigation
--------------------------------------------------------------*/
@media (min-width:1024px){
	.navbar{padding:0;}
	.navbar ul{margin:0; padding:0; display:flex; list-style:none; align-items:center;}
	.navbar li{position:relative; padding: 0 15px;}
    .navbar li:last-child{padding-right: 0;}
	.navbar li a,
	.navbar li a:focus {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #000;
		white-space: nowrap;
		transition: 0.3s;
		text-transform: uppercase;
		padding: 0 0 5px 0;
		font-weight: 500;
        font-size: 14px;
        line-height: 1.5;
	} 
	.navbar li.current-menu-item a,
	.navbar li.current-menu-item:focus a,
    .navbar li.activeNavItem a {
        color:#000;  
    }
    .navbar li a:hover, 
	.navbar li:hover a{
        color:#000;
    }
    .navbar li a:after {
        position: absolute;
        content: '';
        width: 0;
        height: 2px;
        bottom: -3px;
        left: 50%;
        transform: translateX(-50%);
        background: var(--primaryColor);
        -webkit-transition: width 0.3s ease;
        -moz-transition: width 0.3s ease;
        -ms-transition: width 0.3s ease;
        -o-transition: width 0.3s ease;
        transition: width 0.3s ease;
	}
	.navbar li.current-menu-item a:after, .navbar li a:hover:after,
    .navbar li.activeNavItem a:after {width: 100%;}
	.navbar li.menu-item-has-children a:after{
		content: "\f078";
		display: inline-block;
		font-family: 'fontawesome';
		position: absolute;
		top: 50%;
		right: 0;
		transform: translateY(-50%);
		font-size: 12px;
	}

	.navbar li.menu-item-has-children ul.sub-menu {
		display: block;
		position: absolute;
		left: 28px;
		top: calc(100% + 30px);
		margin: 0;
		padding: 10px 0;
		z-index: 99;
		opacity: 0;
		visibility: hidden;
		background: #fff;
		box-shadow: 0px 0px 30px rgba(127, 137, 161, 0.25);
		transition: 0.3s;
		border-radius: 4px;
	}
	.navbar li.menu-item-has-children ul.sub-menu li{min-width:200px;}
	.navbar li.menu-item-has-children ul.sub-menu a {
		padding: 10px 20px;
		font-size: 15px;
		text-transform: none;
		font-weight: 600;
		color: #082744;
	}	
	.navbar li.menu-item-has-children ul.sub-menu a i{font-size:12px;}
	.navbar li.menu-item-has-children ul.sub-menu a:hover,
	.navbar li.menu-item-has-children ul.sub-menu li:hover>a{color:#000000;}

}
@media (min-width:1024px) and (max-width:1366px){
	.navbar .menu-item-has-children .menu-item-has-children ul.sub-menu{left:-90%;}
	.navbar .menu-item-has-children .menu-item-has-children:hover>ul.sub-menu{left:-100%;}
}
@media (min-width:1024px) {.mobile-nav-show, .mobile-nav-hide {display: none;}}

/*--------------------------------------------------------------
# Mobile Navigation
--------------------------------------------------------------*/
@media (max-width:1023px){
	.navbar{position:fixed; top:0; right:-100%; width:100%; max-width:400px; bottom:0; transition:0.3s; z-index:9997;}
	.navbar ul {
		position: absolute;
		inset: 0;
		padding: 50px 0 10px 0;
		margin: 0;
		background: #306178;
		opacity: .95;
		overflow-y: auto;
		transition: 0.3s;
		z-index: 9998;
	}	
	.navbar a,.navbar a:focus{display:flex; align-items:center; justify-content:space-between; padding:10px 20px; font-size:15px; font-weight:600; color:rgba(255,255,255,0.7); white-space:nowrap; transition:0.3s;}
	.navbar a i,.navbar a:focus i{font-size:12px; line-height:0; margin-left:5px;}
	.navbar a:hover,.navbar .current-menu-item a,.navbar .current-menu-item:focus a,.navbar li:hover>a{color:#fff;}
	.navbar .menu-item-has-children ul.sub-menu,.navbar .menu-item-has-children .menu-item-has-children ul.sub-menu{position:static; display:none; padding:10px 0; margin:10px 20px; background-color:rgba(20,35,51,0.6);}
	.navbar .menu-item-has-children>.submenu-active,.navbar .menu-item-has-children .menu-item-has-children>.submenu-active{display:block;}
	.mobile-nav-show{
		font-size: 20px;
		cursor: pointer;
		line-height: 0;
		transition: 0.5s;
		color: #fff;
		padding-right: 30px;
	}
	.mobile-nav-hide {
		color: rgba(255, 255, 255, 0.9);
		font-size: 32px;
		cursor: pointer;
		line-height: 0;
		transition: 0.5s;
		position: fixed;
		right: 15px;
		top: 15px;
		z-index: 9999;
	}
	.mobile-nav-active{overflow:hidden;}
	.mobile-nav-active .navbar{right:0;}
	.mobile-nav-active .navbar:before {
		content: "";
		position: fixed;
		inset: 0;
		background: #000000;
		opacity: .7;
		z-index: 9996;
	}
}
@media (min-width:1023px){.mobile-nav-show,.mobile-nav-hide{display:none !important;}}
.sub-menu-toggle{display: none !important;}
 
/*--------------------------------------------------------------
# Home Video Section
--------------------------------------------------------------*/

.pageMargin {margin-top: 97px;}
.homeVideoSec{position:relative; overflow:hidden;}
.homeVideoSec:after{
    content:""; 
    display:block;
    position:absolute; 
    top:0; 
    left:0; 
    width:100%; 
    height:100%; 
    background: rgb(255 255 255 / 85%); 
}
.homeVideoSecInner{height:calc(85vh - 103px); min-height: 600px; overflow:hidden; position:relative;}
.bannerOption1 .homeVideoSecInner {height: 750px;}
.homeVideoSecInner video{position:absolute; top:0; left:0; width:100%; height:100%; z-index:-1; object-fit:fill;}
.homeVideoSec .homeVideoSecText{top:50%; left:50%; transform:translate(-50%,-50%); width:1140px; max-width:100%; z-index:1; position:absolute;}
.homeVideoSec .homeVideoSecText > div {width: 600px; max-width: 100%; padding: 50px 0;}
.homeVideoSec .homeVideoSecText h1,
.bannerSliderTextBox h1 {
    font-size: 48px;
    font-weight: 700;
    line-height: 1.2;
    text-transform: uppercase;
}
.homeVideoSec .homeVideoSecText p,
.bannerSliderTextBox p {
    color: var(--primaryColor);
    font-size: 22px;
    font-weight: 500;
    line-height: 1.4;                    
}
.homeVideoSec a.btnBanner,
.bannerSliderTextBox a.btnBanner {
    position: relative;
    padding-bottom: 10px;
    margin-top: 5px;
    font-size: 18px;
    font-weight: 600;
    text-transform: uppercase;
}
.homeVideoSec a.btnBanner::before,
.bannerSliderTextBox a.btnBanner::before {
    position: absolute;
    content: "";
    bottom: 0;
    left: 0;
    width: 45px;
    height: 3px;
    background: var(--primaryColor);
}

.homeBannerVectorOverlay {
    position: absolute;
    top: 0;
    right: 0;
    width: 830px;
    height: 100%;
    z-index: 1;
}
.homeBannerVectorOverlay svg {
    
}
.homeBannerVectorOverlay #shapePrimaryColor { 
    fill: var(--primaryColor); width: 400px;
    position: absolute;
    top: 0; left: 250px;
    z-index: 4;
}
.homeBannerVectorOverlay #shapeAccentColor { 
    fill: #999; width: 200px;
    position: absolute;
    bottom: 0; left: 104px; z-index: 3;
}
.homeBannerVectorOverlay .shapeForImgBox { 
    position: absolute;
    height: 670px; 
    width: 65%;
    bottom: 0;
    left: 334px; z-index: 2;
    -webkit-mask-image: url(assets/img/shape-for-img.svg);
    mask-image: url(assets/img/shape-for-img.svg);
    mask-repeat: no-repeat;  
    mask-position: right bottom;

}
.homeBannerVectorOverlay .shapeForImgBox img {
    position: absolute;
    top: 0; left: 0; width: 100%; height: 100%;
    object-fit: cover; object-position: 64% bottom;
    z-index: -1;
}
.homeBannerVectorOverlay .shapeForImgBox #shapeForImg {
    fill: rgb(0 0 0 / 0%); height: 100%; position: relative;
    z-index: 2;
}

.customizeTemplateBtnBox {margin-top: 70px;}

.customizeTemplateBtnBox a {
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    padding: 10px 20px;
    border-radius: 5px;
    background: var(--primaryColor);
    transition: 0.3s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}
.customizeBtnBoxFixed {
    position: fixed;
    top: 98px;
    right: 5px;
    z-index: 999;
}
.customizeBtnBoxFixed a {
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    padding: 10px 20px;
    border-radius: 0 0 5px 5px;
    background: var(--primaryColor);
    transition: 0.3s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}
.animatedShadow {
	position: relative;
	background: linear-gradient(0deg, #000, #272727);
}

.animatedShadow:before, .animatedShadow:after {
	content: '';
	position: absolute;
	left: -1px;
	top: -1px;
	background: linear-gradient(45deg, #fb0094, #0000ff, #00ff00,#ffff00, #ff0000, #fb0094, 
		#0000ff, #00ff00,#ffff00, #ff0000);
	background-size: 70%;
	width: calc(100% + 2px);
	height: calc(100% + 2px);
    border-radius: 5px;
	z-index: -1;
	animation: steam 20s linear infinite;
}

@keyframes steam {
	0% {
		background-position: 0 0;
	}
	50% {
		background-position: 400% 0;
	}
	100% {
		background-position: 0 0;
	}
}

.animatedShadow:after {
	filter: blur(50px);
}
/*--------------------------------------------------------------
# Team Section
--------------------------------------------------------------*/

.teamSec { }
.teamBox .teamBoxImg {
    overflow: hidden; position:relative; display: block;
    z-index: 1;
}
.teamBox .teamBoxImg::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--overlay);
    mix-blend-mode: screen;
    z-index: 2;
    opacity: 0.6;
    transition: 0.3s ease-in-out;
}
.teamBox .teamBoxImg img { 
    filter: grayscale(100%); height: auto !important;
    transition: 0.3s ease-in-out;
    width: 100%; aspect-ratio: 1/1; object-fit: cover;
    object-position: center top;
}
.teamBox.bwHeadshot .teamBoxImg img {
    filter: grayscale(100%);
}
.teamBox.coloredHeadshot .teamBoxImg img {
    filter: grayscale(0);
}
.teamBox:hover .teamBoxImg img {
    transform: scale(1.1);
}
.teamBox h4 { 
    text-transform: uppercase;
    font-weight: 600;
    font-size: 20px;
    line-height: 20px;
    margin: 10px 0 0 0;
}
.teamBox span { 
    text-transform: uppercase; 
    font-size: 16px;
    color: #555555;
}

/*--------------------------------------------------------------
# Title Section
--------------------------------------------------------------*/

.sectionTitle { 
    position:relative;
    text-align: center;
    margin: 0 0 60px 0;
}
.sectionTitle h2 {
    position: relative;
    display: inline-block;
    font-size: 32px;
    text-transform: uppercase;
    font-weight: 400; 
    padding: 0 0 10px 0; 
}
.sectionTitle h2 strong { 
    font-weight: 600;   
}
.sectionTitle h2:before {
    content: "";
    position: absolute;
    background: var(--secondaryColor);
    width: 75px;
    height: 6px;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}
.sectionTitle p {
    font-size: 15px;
    margin: 30px 0 0 0;
}
.sectionTitleWhite h2 {
    color: #ffffff;
}


/*--------------------------------------------------------------
# What Happening Section
--------------------------------------------------------------*/

.whatHappenSec {}
.articleBoxesContainer > div:nth-child(-n+3) {border-bottom: 1px solid rgb(0 0 0 / 20%);}
.articleBoxesContainer > div:nth-child(2),
.articleBoxesContainer > div:nth-child(5) {border-left: 1px solid rgb(0 0 0 / 20%); border-right: 1px solid rgb(0 0 0 / 20%);}
.whatHappenBox {
    padding: 60px 40px; position: relative; z-index: 1;
    -webkit-transition: 0.3s ease-in-out;
    -moz-transition: 0.3s ease-in-out;
    -ms-transition: 0.3s ease-in-out;
    -o-transition: 0.3s ease-in-out;
    transition: 0.3s ease-in-out;
}
.whatHappenBox::before {content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--secondaryColor);
    z-index: -1;
    opacity: 0;
    transition: 0.3s ease-in-out;
}
.whatHappenBox h4 {
    text-transform: uppercase;
    font-weight: 600;
    font-size: 20px;
    margin-bottom: 15px;
}
.whatHappenBox p {
    font-size: 15px;
    margin-bottom: 20px;
}
.whatHappenBox a {
    font-size: 14px;
    color: #000000;
}
.whatHappenBox:hover:before {
    opacity: 0.15;
}
.blogModal .modalHeaderContainer {border-bottom: none; min-height: 62px;}
.blogModal .modalBodyContainer {box-shadow: none; background-color: #fff;}

/*--------------------------------------------------------------
# Subscribe Section
--------------------------------------------------------------*/

.transactionSec {
    position:relative;
    z-index: 1;
}
.transactionSec:before{
    content:''; 
    position:absolute; 
    top:0; 
    left:0;  
    width:100%; 
    height:100%; 
    z-index: -1;
    background: rgb(0 0 0 / 65%);
}
.transactionBox .transactionBoxImg {
    overflow: hidden;
}
.transactionBox .transactionBoxImg img {
    /* filter: grayscale(100%); */
    transform: scale(1);
    transition: 0.3s ease-in-out;
}
.transactionBox:hover .transactionBoxImg img {
    /* filter: grayscale(0);  */
    transform: scale(1.1);
}
.transactionBox.bwTransactionImage .transactionBoxImg img {
    filter: grayscale(100%);
}
.transactionBox.coloredTransactionImage .transactionBoxImg img {
    filter: grayscale(0);
}
.transactionBox.transactionImageHoverBW:hover .transactionBoxImg img {
    filter: grayscale(100%);
}
.transactionBox.transactionImageHoverColored:hover .transactionBoxImg img {
    filter: grayscale(0);
}
.transactionBox h4 {
    font-size: 19px;
    color: #ffffff;
    text-transform: uppercase;
    font-weight: 600;
    margin: 12px 0 0 0;
    line-height: 24px;
}
.transactionBox a {
    text-transform: uppercase;
    font-size: 15px;
    color: #bbbbbb;
}
.transactionCarousel {
    position: relative;
}

.transactionCarousel .owl-nav button.owl-prev span,
.transactionCarousel .owl-nav button.owl-next span {
    background: rgba(0, 0, 0, 0.6) !important;
    color: #fff !important;
    padding: 12px 16px !important;
    font-size: 20px !important;
    border-radius: 4px !important;
    display: inline-flex !important;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.transactionCarousel .owl-nav button.owl-prev span:hover,
.transactionCarousel .owl-nav button.owl-next span:hover {
    background: rgba(0, 0, 0, 0.85) !important;
}
.transactionCarousel .owl-nav {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    width: 100%;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    pointer-events: none; /* So arrows don't block carousel drag */
}

.transactionCarousel .owl-nav button {
    pointer-events: auto; /* So buttons still work */
}



/*--------------------------------------------------------------
# Footer
--------------------------------------------------------------*/

.footerSec {  
    position: relative;
    
    z-index: 1;
}
.footerSec:before {
    content:''; 
    position:absolute; 
    top:0; 
    left:0;
    width:100%; 
    height:100%; 
    z-index: -1;
    background: rgb(0 0 0 / 88%);
}
.footerSec .footerSecTop {
    border-bottom: 2px solid #777777;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

.footerSec .footerSecSignUp { 
    text-align: center;
}
.footerSec .footerSecSignUp input[type="text"],
.footerSec .footerSecSignUp textarea {
    color: #000000;
}
.footerSec .footerSecSignUp h3 { 
    font-size: 30px;
    line-height: 34px;
    font-weight: 500;
    color: #ffffff; 
    text-transform: uppercase; 
    margin-bottom: 15px;
}
.footerSec .footerSecSignUp p { 
    color: #ffffff;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    margin-bottom: 50px;
}
.footerSec .footerSecSignUp .form-fields {
    background: #ffffff;
    padding: 15px 20px;
}
.footerSec .footerSecSignUp .wpcf7-submit {
    position: absolute;
    padding: 14px 60px;
    margin-left: -180px;
    border: 1px solid #ffffff;
    background: var(--primaryColor);
}
.footerSec .footerSecSignUp .wpcf7-submit:hover {
    border: none;
    background: #000000;
    color: #ffffff;
    border: 1px solid #ffffff;
}

.footerSec .footerSecCenter .footerSecCol img {
    width: 300px;
    margin-bottom: 80px;
} 

.footerSec .footerSecCol .footerSocial li {
   display: inline-block; 
} 
.footerSec .footerSecCol .footerSocial li a i {
    display: inline-block;
    font-size: 15px;
    color: #273e63;
    background: #ffffff;
    width: 30px;
    height: 30px;
    line-height: 32px;
    text-align: center;
    border-radius: 50%;
    margin-right: 5px;
} 

.footerSec .footerSecCenter {
} 
.footerSec .footerSecCol h4 { 
    font-size: 22px;
    font-weight: 400;
    color: #ffffff; 
    text-transform: uppercase; 
    margin-bottom: 15px;
} 

.footerSec .footerSecCol .footerPageLink li a {
    color: #ffffff;
    font-size: 14px;
    line-height: 26px;
    font-weight: 400;
}
.footerSec .footerSecCol .footerPageLink li a:hover {
    font-weight: 600;
}

.footerSec .footerSecCol .footerInfo p {
    color: #ffffff;
    font-size: 14px;
    line-height: 26px; 
    margin-bottom: 20px;
}
.footerSec .footerSecCol .footerInfo a {
    color: #ffffff;
    font-size: 14px; 
    line-height: 2; 
    display: block;
}
.footerSec .footerSecCol .footerInfo a:hover {
    font-weight: 600;
}
.footerSec .footerSecCol .footerInfo a i {
    display: inline-block;
    font-size: 15px;
    color: #273e63;
    background: #ffffff;
    width: 30px;
    height: 30px;
    line-height: 32px;
    text-align: center;
    border-radius: 50%;
    margin-right: 5px;
} 

.footerSecBottom {
    text-align: center; 
    padding: 0 0 30px 0;
}
.footerSecBottom p {
    font-size: 13px;
    margin-bottom: 0; 
    color:rgb(255 255 255 / 50%); 
    font-weight: 400;
}
.footerSecBottom p a {
    color:rgb(255 255 255 / 50%); 
}
.footerSecBottom p a:hover {
    text-decoration: underline;
    color: #ffffff;
}

.form-fields,.form-fields2,.form-fields3{width:100% !important; box-sizing:border-box; padding:5px 0; font-size:15px; margin-bottom:15px; background:none; border:none; color: #ffffff; border-bottom:1px solid #555555; -webkit-transition:0.3s ease-in-out !important; -moz-transition:0.3s ease-in-out !important; -ms-transition:0.3s ease-in-out !important; -o-transition:0.3s ease-in-out !important; transition:0.3s ease-in-out !important;}
.form-fields:focus,.form-fields2:focus,.form-fields3:focus{border:none; border-bottom:1px solid #555555; outline:none !important;}
.form-fields3{height:130px;}
.wpcf7-submit,.ln-widgetBox.search .searchButton a {
    float:right; 
    position:relative; 
    padding:10px 60px; 
    font-weight:500; 
    display:inline-block; 
    text-transform:uppercase; 
    border:none; 
    background: transparent; 
    color:#dddddd;  
    border: 1px solid #555555;
    -webkit-transition: 0.3s ease-in-out !important;
    -moz-transition: 0.3s ease-in-out !important;
    -ms-transition: 0.3s ease-in-out !important;
    -o-transition: 0.3s ease-in-out !important;
    transition: 0.3s ease-in-out !important;
}
.wpcf7-submit:hover{border:none; background:#ffffff; color:#000000; border: 1px solid #ffffff;}
div.wpcf7 img.ajax-loader{float:left;}
.wpcf7-list-item{display:inline-block; margin-right:10px;}
div.wpcf7-response-output{float:left;}
.wpcf7-not-valid-tip{display:none !important;}
.wpcf7-not-valid{border-bottom:1px solid #b31926 !important; border:none;}
::placeholder{font-size:16px; text-transform:capitalize; color:#555555;}
.wpcf7 form.invalid .wpcf7-response-output,.wpcf7 form.unaccepted .wpcf7-response-output,.wpcf7 form.payment-required .wpcf7-response-output{border-color:#555555; color:#ffffff;}

/*--------------------------------------------------------------
# Services Section
--------------------------------------------------------------*/

.servicesSec {}

.servicesBox { 
    border: 1px solid #dddddd;
    padding: 40px 30px;
    display: block;
    height: 100%;
    font-weight: 400; position: relative; z-index: 1;
    -webkit-transition: 0.3s ease-in-out;
    -moz-transition: 0.3s ease-in-out;
    -ms-transition: 0.3s ease-in-out;
    -o-transition: 0.3s ease-in-out;
    transition: 0.3s ease-in-out;
}
.servicesBox::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--secondaryColor);
    z-index: -1;
    opacity: 0;
    transition: 0.3s ease-in-out;
}
.servicesBox:hover::before {
    opacity: 0.15;
}
.servicesBox img { 
    height: 50px;
}

.servicesBox h3 { 
    text-transform: uppercase;
    font-weight: 600;
    font-size: 20px;
    line-height: 20px;
    margin: 10px 0 20px 0;
}
.servicesBox p {  
    font-size: 15px;
}



/*--------------------------------------------------------------
# About Section
--------------------------------------------------------------*/
 
.aboutSecImg {
    height: 100% !important;
    width: 100% !important;
    max-width: 100% !important;
    object-fit: cover;
    object-position: center;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
}
.aboutTextBorder {
    position: relative;
    /* top: 80px;
    left: 0; */
    display: block;
    width: 400px;
}
.aboutTextBorder::before {
    position: absolute;
    content: "";
    top: 50%;
    left: 0;
    transform: translate(0, -50%);
    width: 70%;
    height: 6px;
    background: #000000;
}
.aboutTextBorder::after {
    position: absolute;
    content: "";
    top: 50%;
    right: 0;
    transform: translate(0, -50%);
    width: 30%;
    height: 6px;
    background: var(--secondaryColor);
}

.aboutText {  
    /* padding: 150px 240px 150px 120px; */
} 
.aboutText h3 { 
    text-transform: uppercase;
    font-weight: 600;
    font-size: 30px;
    line-height: 36px;
    margin: 0 0 25px 0;
}
.aboutText p {  
    font-size: 16px;
} 

.aboutCounter {
    padding-top: 80px;
}
.aboutCounter .counterBox h2 {
    font-weight: 700; font-size: 36px;
}
.aboutCounter p {
    position: relative;
    padding-bottom: 10px;
    margin: 0;
}
.aboutCounter p::before {
    position: absolute;
    content: "";
    bottom: 0;
    left: 0;
    width: 45px;
    height: 3px;
    background: var(--primaryColor);
}


/*--------------------------------------------------------------
# Home Additional Section 5
--------------------------------------------------------------*/

.testimonialSec {
    background: #dddddd;
} 
.testimonialImg{position:absolute; top:0; left:0; height:100%;}

.testimonialBox{position:relative; background:#ffffff; padding:40px 40px 60px 40px; margin:50px 0 120px 0; border:1px solid #eeeeee; box-shadow:5px 10px 10px 0 rgba(0,0,0,.3);}
.testimonialBox h3{font-weight:700; font-size:18px; line-height:22px; color:var(--primaryColor);}
.testimonialBox p{font-size:14px;}
.testimonialBoxImg{position:absolute; bottom:-50px; left:50%; transform:translateX(-50%); background:#ffffff; border:2px solid var(--primaryColor); border-radius:50%; overflow: hidden; width:100px; height:100px; display:flex; align-items:center; justify-content:center;}
.testimonialBoxImg img{width: 100%;}

.testimonialBottom{margin-bottom:50px;}
.testimonialBottom div{display:flex; justify-content:space-between; position:relative; padding-bottom:5px; margin-bottom:20px;}
.testimonialBottom div:after{content:""; position:absolute; bottom:0; left:0; width:100%; height:2px;}
.testimonialBottom .top h3{font-weight:700; font-size:18px; line-height:28px; color:var(--primaryColor); margin:0;}
.testimonialBottom .top::before,
.testimonialBottom .bottom::before {content:""; position:absolute; 
    bottom:0; left:0; width:100%; height:4px; background:var(--secondaryColor); 
    transition:width 2s ease-in-out; z-index:1;
}
.testimonialBottom .top::after,
.testimonialBottom .bottom::after {content:""; position:absolute; 
    bottom:0; left:0; width:0%; height:4px; background:var(--primaryColor); 
    transition:width 2s ease-in-out; z-index:2;
}
.testimonialBottom .top.active::after,
.testimonialBottom .bottom.active::after {width:100%;}
.testimonialBottom .bottom h3{font-weight:700; font-size:18px; line-height:28px; margin:0;}


/*--------------------------------------------------------------
# Contact Section
--------------------------------------------------------------*/

.contactSec .contactSecForm {
    position: relative;
    z-index: 1;
}
.contactSec .contactSecForm:before{
    content:''; 
    position:absolute; 
    top:0; 
    left:0;
    width:100%; 
    height:100%; 
    z-index: -1;
    background: rgb(0 0 0 / 85%);
}

.contactSec .contactSecInfo {
    padding: 100px; 
    position: relative;
    z-index: 1;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
}
.contactSec .contactSecInfo:before{
    content:''; 
    position:absolute; 
    top:0; 
    left:0;
    width:100%;
    height:100%; 
    z-index: 9999;
    background: rgb(0 0 0 / 85%);
}
.contactSecInfoInner{
    position: relative;
    z-index: 9999;
}
.contactSecInfoInner ul li {
    display: flex;
    flex-wrap: nowrap;
    gap: 20px;
    margin-bottom: 30px;
    border: 1px solid #555555;
    width: 400px;
    align-items: center;
    background-color: transparent;
    transition: all 0.3s ease-in-out;
}
.contactSecInfoInner ul li:hover {
    background-color: rgb(0 0 0 / 60%);
}
.contactSecInfoInner ul li .icon {
    width: 90px;
    padding: 22px 15px;
    font-size: 22px;
    text-align: center;
    background: rgb(0 0 0 / 40%);
}
.contactSecInfoInner ul li .icon i {
    color: #ffffff;
}
.contactSecInfoInner ul li .text {
    width: calc(100% - 50px);
}
.contactSecInfoInner ul li .text h4 {
    font-size: 15px;
    line-height: 20px;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
    text-transform: uppercase;
}
.contactSecInfoInner ul li .text a {
    font-size: 14px;
    font-weight: 400;
    color: #888888; 
    line-height: 18px;
}
.contactSecInfoInner ul li .text p {
    font-size: 14px;
    font-weight: 400;
    color: #888888;
    line-height: 16px;
    margin: 0;
}
  
.contactSecFormInner h2 { 
    font-size: 38px;
    text-transform: uppercase;
    font-weight: 400;
    margin-bottom: 0;
    color: #ffffff;
}
.contactSecFormInner h2 strong {
    font-weight: 600;
    color: #ffffff;
}
.contactSecFormInner h5 { 
    color: #bbbbbb;
    margin: 25px 0 40px 0;
    font-size: 16px;
}

/*--------------------------------------------------------------
# listing Section
--------------------------------------------------------------*/


.listingInner { 
    display: flex;
    flex-wrap: wrap;
    position: relative;
}
.listingInner img {
    max-width: 100%; 
}
.listingInner .listingBox {
    position: relative; 
}
.listingInner .listingBox:before {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg,rgba(0, 0, 0, 1) 20%, rgba(0, 0, 0, 0.4) 80%, transparent 100%);
    opacity: 0.8;
    transition: 0.3s;
}
.listingInner .listingBox:hover:before {
    opacity: 0.9;
}
.listingBox .listingBoxInfo {
    position: absolute;
    top: 0;
    z-index: 1;
    padding: 40px 50px;
    width: 100%;
    height: 100%;
    transition: 0.3s;
}
.listingBox:hover .listingBoxInfo {
    top: 20px;
}
.listingBox .listingBoxInfo h4 {
    font-size: 20px;
    color: #ffffff;
    margin-bottom: 0;
}
.listingBox .listingBoxInfo span { 
    color: #bbbbbb;
}
.listingBox .listingBoxInfo h5 { 
    font-size: 20px;
    color: #ffffff;
    margin: 20px 0 0 0;
}
.listingBox .listingBoxInfo a { 
    color: #bbbbbb;
}


/* Navigation positioning stays on the buttons */
.listingInner .owl-nav {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    pointer-events: none;
    z-index: 10;
}

.listingInner .owl-nav button.owl-prev,
.listingInner .owl-nav button.owl-next {
    pointer-events: all;
    border: none;
    background: none;
    padding: 0;
    cursor: pointer; border-radius: 4px; outline: none;
}

/* Style the inner <span> icon */
.listingInner .owl-nav button span {
    display: inline-block;
    background: rgba(0, 0, 0, 0.4);
    color: #fff;
    padding: 10px 15px;
    font-size: 20px;
    border-radius: 4px;
    transition: background 0.3s ease;
}

/* Hover effect on span */
.listingInner .owl-nav button:hover span {
    background: rgba(0, 0, 0, 0.7);
}



.homeTenantLogosSec {
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
}
.owl-carousel-logos .owl-item{ text-align: center;}
.owl-carousel-logos .owl-item img {max-width: 160px; margin: auto;}

/* Dark Theme Styles Start */

body.darkTheme {background-color: #000;}
body.darkTheme * {color: #fff;}
body.darkTheme #header {background-color: #000;}
body.darkTheme #header .logo img {filter: invert(100%);}
body.darkTheme .navbar li.current-menu-item a,body.darkTheme .navbar li.current-menu-item:focus a,
body.darkTheme .navbar li.activeNavItem a {color: #fff;}
body.darkTheme .navbar li a,body.darkTheme .navbar li a:focus,
body.darkTheme .navbar li.activeNavItem a,body.darkTheme .navbar li.activeNavItem a:focus {color: #fff;}
/* body.darkTheme .navbar li a:after {background: var(--secondaryColor);} */
body.darkTheme .homeVideoSec:after {background: rgba(0, 0, 0, 0.7);}
body.darkTheme .homeVideoSec .homeVideoSecText p {color: #fff;}
/* body.darkTheme .homeBannerVectorOverlay #shapePrimaryColor {fill: var(--secondaryColor);} */
body.darkTheme .homeBannerVectorOverlay #shapeAccentColor {opacity: 0.5;}
/* body.darkTheme .homeVideoSec a.btnBanner,
body.darkTheme .bannerSliderTextBox a.btnBanner {color: var(--secondaryColor);} */
/* body.darkTheme .homeVideoSec a.btnBanner::before, */
/* body.darkTheme .bannerSliderTextBox a.btnBanner::before {background: var(--secondaryColor);} */
body.darkTheme .customizeTemplateBtnBox a {
    color: var(--primaryColor); background: #000;
    border: 1px solid var(--primaryColor);
}
body.darkTheme .customizeTemplateBtnBox a i {color: var(--primaryColor); transition: all 0.3s ease;}
body.darkTheme .customizeTemplateBtnBox a:hover {
    color: #fff; background: var(--primaryColor);
    border: 1px solid var(--primaryColor);
}
body.darkTheme .customizeTemplateBtnBox a:hover i {color: #fff;}
body.darkTheme .aboutTextBorder::before {background: #fff;}
/* body.darkTheme .aboutCounter p::before {background: var(--secondaryColor);} */
body.darkTheme .owl-carousel-logos .owl-item img {filter: invert(100%) grayscale(80%);}
body.darkTheme .servicesBox {
    background: #333; border: none
}
body.darkTheme .servicesBox:hover {background: #444;}
body.darkTheme .customizeBtnBoxFixed a {color: var(--primaryColor);
    background: #000; border: 1px solid var(--primaryColor);
}
body.darkTheme .customizeBtnBoxFixed a i {color: var(--primaryColor); transition: all 0.3s ease;}
body.darkTheme .customizeBtnBoxFixed a:hover {color: #fff;
    background: var(--primaryColor); border: 1px solid var(--primaryColor);
}
body.darkTheme .customizeBtnBoxFixed a:hover i {color: #fff;}
body.darkTheme .sectionTitle h2:before {background: var(--primaryColor);}
body.darkTheme .articleBoxesContainer > div:nth-child(-n+3) {border-bottom: 1px solid rgb(255 255 255 / 20%);}
body.darkTheme .articleBoxesContainer > div:nth-child(2),
body.darkTheme .articleBoxesContainer > div:nth-child(5) {border-left: 1px solid rgb(255 255 255 / 20%); border-right: 1px solid rgb(255 255 255 / 20%);}
body.darkTheme .whatHappenBox:hover {background: #333;}
body.darkTheme .testimonialSec {background: #000; border-top: 1px solid #333;}
body.darkTheme .testimonialBox * {color: #000;}
body.darkTheme .testimonialBox h3 {color: var(--primaryColor);}
body.darkTheme .testimonialBottom .top h3 {color: #fff;}
body.darkTheme .testimonialBottom .top::before,body.darkTheme .testimonialBottom .bottom::before {
    background: var(--primaryColor);
}
body.darkTheme .testimonialBottom .top::after,
body.darkTheme .testimonialBottom .bottom::after {background: var(--secondaryColor);}


body.darkTheme .footerSecBottom p {color: rgb(255 255 255 / 50%);}

/* Dark Theme Styles End */



.modal *, body.darkTheme .modal *:not(.btn-primary,.teamInfoBox a, .teamInfoBox a i) {color: #000;}
.modal .modal-dialog {width: 95%; max-width: initial; border-radius: 0;}
.modal-content {border-radius: 0; height: 95vh; overflow-y: auto;}
.modal-header {border-bottom: none;}
.modalHeaderContainer {border-bottom: 1px solid #ddd; position: relative;}
.modalHeaderContainer .btn-close {position: absolute; 
    top: 22px; right: 15px; font-size: 20px; cursor: pointer; 
    color: #000; transition: all 0.3s ease;
}
.modalHeaderContainer .btn-close:hover {color: #000; opacity: 0.7;}
.modalBodyContainer {height: 100%; overflow-y: auto;}

.bulletText ul {
  list-style: none;
  padding-left: 0;
  display: flex;
  flex-direction: column;
  gap: 8px 0;
  margin: 20px 0;
}

.bulletText ul li {
  padding-left: 15px;
  border-left: 5px solid var(--secondaryColor);
  position: relative;
}
.idScrollFix {margin-top: -98px; padding-top: 98px;}

.listingContentBox ul {
  display: grid;
  grid-template-columns: 1fr 1fr;
  list-style: none;
  padding: 0;
  margin: 20px 0 20px;
  row-gap: 10px;
  column-gap: 20px;
  background: #f9f9f9;
  padding: 15px;
  border: 1px solid #ddd;
}

.listingContentBox ul li {
  padding: 6px 0;
  border-bottom: 1px solid #eee;
  line-height: 1.5;
}

.listingContentBox ul li:nth-last-child(-n+2) {
  border-bottom: none;
}

.listingContentBox ul li:nth-child(odd) {
  font-weight: 600;
}

.listingContentBox ul li:nth-child(even) {}
.listingAgentFormBox textarea {height: 70px;}
.listingAgentFormBox .wpcf7-submit {border-color: var(--primaryColor); color: var(--primaryColor);}
.listingAgentFormBox .wpcf7-submit:hover {border-color: var(--primaryColor); background: var(--primaryColor); color: #fff;}
.listingGalleryWrapper {
    margin-bottom: 20px;
}
.thumbItem {
    cursor: pointer;
    border: 2px solid transparent;
    padding: 3px;
    border-radius: 4px;
}
.thumbItem.active {
    border-color: var(--primary, #007bff);
}
.thumbItem img {
    width: 100%;
    height: 100px;
    object-fit: cover;
    object-position: center;
    border-radius: 4px;
}

.thumbItem {
    cursor: pointer;
    border: 2px solid transparent;
    padding: 3px;
    border-radius: 4px;
    transition: border-color 0.3s ease;
}

.thumbItem.active {
    border-color: var(--primary, #007bff);
}
.fancybox__container {z-index: 9999;}

.btnPrimary {
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    padding: 10px 20px;
    border-radius: 5px;
    background: var(--primaryColor);
    transition: 0.3s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}
.btnPrimary:hover,
.btnPrimary:focus {
    background: var(--primaryColor);
    color: #fff;
    text-decoration: none;
}

.teamInfoBox {
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #e6e6e6;
}

.teamInfoBox h3 {
    font-size: 24px;
    font-weight: 600;
    color: #222;
    margin: 0;
    line-height: 1.2;
}

.teamInfoBox h4 {
    font-size: 16px;
    font-weight: 400;
    color: #777;
    margin-top: 5px;
    margin-bottom: 0;
}

.teamInfoBox a {
    color: var(--primaryColor) !important;
    font-size: 16px;
    transition: color 0.3s ease, transform 0.3s ease;
}
.teamInfoBox a i {
    color: var(--primaryColor) !important;
    transition: color 0.3s ease, transform 0.3s ease;
}

.teamInfoBox a:hover, .teamInfoBox a:focus,
.teamInfoBox a:hover i, .teamInfoBox a:focus i {
    color: var(--secondaryColor) !important;
}

.teamInfoBox i {
    font-size: 18px;
}
.teamModalImgBox img {
    width: 100%;
    height: auto !important;
    aspect-ratio: 1/1;
    object-fit: cover;
    object-position: center top;
}

.bannerOption2 .homeBannerVectorOverlay,
.bannerOption2 .bannerImageSliderSec,
.bannerOption3 .homeVideoSec,
.bannerOption1 .bannerImageSliderSec {display: none;}

.bannerImageSliderSec {position: relative;  overflow: hidden;}
.bannerImageSliderSec .bannerSliderTextBox > div {min-height: calc(85vh - 103px);}
.bannerImageSliderSec .bannerImageSlider {
    position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: -1;
}
.bannerImageSliderSec .bannerImageSlider .owl-stage-outer,
.bannerImageSliderSec .bannerImageSlider .owl-stage-outer div {height: 100% !important;}
.bannerImageSliderSec .bannerImageSlider .bannerSliderImageBox {position: relative; z-index: 0;}
.bannerImageSliderSec .bannerImageSlider .bannerSliderImageBox::before {
    content: ''; position: absolute; top: 0; left: 0; 
    width: 100%; height: 100%; background: rgba(255, 255, 255, 0.85); z-index: 1;
}
body.darkTheme .bannerImageSliderSec .bannerImageSlider .bannerSliderImageBox::before {background: rgba(0, 0, 0, 0.6);}
.bannerImageSliderSec .bannerImageSlider img {width: 100% !important; height: 100% !important;
    object-fit: cover; object-position: center; display: block;
    max-width: 100% !important;
}

.modalBodyContainer {
    background-color: #f9f9f9;
    border-radius: 1rem;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    padding-top: 2rem;
    padding-bottom: 2rem;
}

#customizeModalContent > div {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid #dee2e6;
}

#customizeModalContent > div:last-child {
    border-bottom: none;
}

#customizeModalContent h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #092a63;
}

#customizeModalContent h4 {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #444;
}

.btn-check:checked+.btn-primary, .btn-check:active+.btn-primary, 
.btn-primary:active, .btn-primary.active, .show>.btn-primary.dropdown-toggle, 
.activeThemeBtn, .activeBannerOptionBtn, .activeHeadshotStyleBtn, 
.activeTransactionStyleBtn, .activeTransactionHoverStyleBtn {
    background-color: #002b6a !important;
    box-shadow: 0 0 0 0.25rem rgba(49, 132, 153, 0.5);
}

.mainBannerOptions{display: block !important; float: left; width: 100%;}
.mainBannerOptions div{float: left; padding: 0.5rem;}
.mainBannerOptions img{width: 100%;}
.mainBannerOptions .btn-primary{display: block; padding: 0.2rem 1rem 1rem;}

.colorPicker label, label[for="teamOverlayColorPicker"]{
    display: block !important;
}

#customizeModalContent label {
    display: inline-block;
    margin-right: 0.5rem;
    font-weight: 500;
    color: #333;
}

.colorPicker input[type="color"],
#teamOverlayColorPicker {
    border: none;
    width: 48px;
    height: 32px;
    padding: 0;
    cursor: pointer;
    background: none;
}

.btn-primary {
    margin: 0.25rem 0.5rem 0.25rem 0;
    border-radius: 0.2rem;
    padding: 0.2rem 1rem;
    font-size:15px;
    color: #ffffff;
    background-color: #999999;
    border-color: #999999;
}
.btn-primary:hover{background-color: #002b6a;}

.btn-primary:focus,
.btn-primary:active {
    outline: none;
    box-shadow: 0 0 0 0.25rem rgba(81, 203, 255, 0.4);
}

input[type="radio"] {
    margin-left: 0.5rem;
    margin-right: 0.25rem;
}

@media (min-width: 768px) {
    #customizeModalContent > div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
    }

    #customizeModalContent h3 {
        flex: 1 1 200px;
        margin-bottom: 0.5rem;
    }

    #customizeModalContent > div > div {
        flex: 2 1 400px;
    }
}
.customizeModal .modalHeaderContainer .btn-close {
    font-size: 14px;
    width: auto;
    background: #002b6a;
    color: #fff;
    opacity: 1;
    height: auto;
    padding: 15px 15px;
    top: 18px;
    line-height: 1;
}
.darkTheme .customizeModal .modalHeaderContainer .btn-close {color: #fff;}

.radio-group{display: flex; gap: 1rem;}
/* Hide the actual radio input */
.radio-group .btn-radio input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

/* Style the label as a button */
.radio-group  .btn-radio {
    position: relative;
    display: inline-block;
}

.radio-group  .btn-radio label {
    display: inline-block;
    padding: 8px 16px;
    background-color: #999999;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    text-align: center;
    user-select: none;
    color: #ffffff !important;
}
.radio-group  .btn-radio label img{max-width: 100%; height: 160px; object-fit: cover;}

/* Hover effect */
.radio-group  .btn-radio label:hover {
    background-color: #002b6a !important;
    border-color: #002b6a;
}

/* Checked state - highlighted */
.radio-group  .btn-radio input[type="radio"]:checked + label {
    background-color: #002b6a !important;
    border-color: #002b6a;
    color: white;
    box-shadow: 0 0 0 0.25rem rgba(49, 132, 153, 0.5);
}

/* Focus state for accessibility */
.radio-group  .btn-radio input[type="radio"]:focus + label {
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(49, 132, 153, 0.5);
}