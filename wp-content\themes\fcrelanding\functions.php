<?php

/**
 * Functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package WordPress
 * @subpackage Twenty_Twenty_One
 * @since Twenty Twenty-One 1.0
 */


function add_file_types_to_uploads($file_types)
{
	$new_filetypes = array();
	$new_filetypes['svg'] = 'image/svg+xml';
	$file_types = array_merge($file_types, $new_filetypes);
	return $file_types;
}
add_action('upload_mimes', 'add_file_types_to_uploads');

// Register custom image size for team members
function register_custom_team_image_size()
{
	// 400x400, hard crop from center horizontally and top vertically
	add_image_size('team-member-photo', 400, 400, ['center', 'top']);
}
add_action('after_setup_theme', 'register_custom_team_image_size');


// This theme requires WordPress 5.3 or later.
if (version_compare($GLOBALS['wp_version'], '5.3', '<')) {
	require get_template_directory() . '/inc/back-compat.php';
}

if (! function_exists('twenty_twenty_one_setup')) {
	/**
	 * Sets up theme defaults and registers support for various WordPress features.
	 *
	 * Note that this function is hooked into the after_setup_theme hook, which
	 * runs before the init hook. The init hook is too late for some features, such
	 * as indicating support for post thumbnails.
	 *
	 * @since Twenty Twenty-One 1.0
	 *
	 * @return void
	 */
	function twenty_twenty_one_setup()
	{
		/*
		 * Make theme available for translation.
		 * Translations can be filed in the /languages/ directory.
		 * If you're building a theme based on Twenty Twenty-One, use a find and replace
		 * to change 'twentytwentyone' to the name of your theme in all the template files.
		 */
		load_theme_textdomain('twentytwentyone', get_template_directory() . '/languages');

		// Add default posts and comments RSS feed links to head.
		add_theme_support('automatic-feed-links');

		/*
		 * Let WordPress manage the document title.
		 * This theme does not use a hard-coded <title> tag in the document head,
		 * WordPress will provide it for us.
		 */
		add_theme_support('title-tag');

		/**
		 * Add post-formats support.
		 */
		add_theme_support(
			'post-formats',
			array(
				'link',
				'aside',
				'gallery',
				'image',
				'quote',
				'status',
				'video',
				'audio',
				'chat',
			)
		);

		/*
		 * Enable support for Post Thumbnails on posts and pages.
		 *
		 * @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
		 */
		add_theme_support('post-thumbnails');
		set_post_thumbnail_size(1568, 9999);

		register_nav_menus(
			array(
				'header' => esc_html__('Header menu', 'twentytwentyone'),
				'footer'  => esc_html__('Footer menu', 'twentytwentyone'),
			)
		);

		/*
		 * Switch default core markup for search form, comment form, and comments
		 * to output valid HTML5.
		 */
		add_theme_support(
			'html5',
			array(
				'comment-form',
				'comment-list',
				'gallery',
				'caption',
				'style',
				'script',
				'navigation-widgets',
			)
		);

		/*
		 * Add support for core custom logo.
		 *
		 * @link https://codex.wordpress.org/Theme_Logo
		 */
		$logo_width  = 300;
		$logo_height = 100;

		add_theme_support(
			'custom-logo',
			array(
				'height'               => $logo_height,
				'width'                => $logo_width,
				'flex-width'           => true,
				'flex-height'          => true,
				'unlink-homepage-logo' => true,
			)
		);

		// Add theme support for selective refresh for widgets.
		add_theme_support('customize-selective-refresh-widgets');

		// Add support for Block Styles.
		add_theme_support('wp-block-styles');

		// Add support for full and wide align images.
		add_theme_support('align-wide');

		// Add support for editor styles.
		add_theme_support('editor-styles');
		$background_color = get_theme_mod('background_color', 'D1E4DD');
		if (127 > Twenty_Twenty_One_Custom_Colors::get_relative_luminance_from_hex($background_color)) {
			add_theme_support('dark-editor-style');
		}

		$editor_stylesheet_path = './assets/css/style-editor.css';

		// Note, the is_IE global variable is defined by WordPress and is used
		// to detect if the current browser is internet explorer.
		global $is_IE;
		if ($is_IE) {
			$editor_stylesheet_path = './assets/css/ie-editor.css';
		}

		// Enqueue editor styles.
		add_editor_style($editor_stylesheet_path);

		// Add custom editor font sizes.
		add_theme_support(
			'editor-font-sizes',
			array(
				array(
					'name'      => esc_html__('Extra small', 'twentytwentyone'),
					'shortName' => esc_html_x('XS', 'Font size', 'twentytwentyone'),
					'size'      => 16,
					'slug'      => 'extra-small',
				),
				array(
					'name'      => esc_html__('Small', 'twentytwentyone'),
					'shortName' => esc_html_x('S', 'Font size', 'twentytwentyone'),
					'size'      => 18,
					'slug'      => 'small',
				),
				array(
					'name'      => esc_html__('Normal', 'twentytwentyone'),
					'shortName' => esc_html_x('M', 'Font size', 'twentytwentyone'),
					'size'      => 20,
					'slug'      => 'normal',
				),
				array(
					'name'      => esc_html__('Large', 'twentytwentyone'),
					'shortName' => esc_html_x('L', 'Font size', 'twentytwentyone'),
					'size'      => 24,
					'slug'      => 'large',
				),
				array(
					'name'      => esc_html__('Extra large', 'twentytwentyone'),
					'shortName' => esc_html_x('XL', 'Font size', 'twentytwentyone'),
					'size'      => 40,
					'slug'      => 'extra-large',
				),
				array(
					'name'      => esc_html__('Huge', 'twentytwentyone'),
					'shortName' => esc_html_x('XXL', 'Font size', 'twentytwentyone'),
					'size'      => 96,
					'slug'      => 'huge',
				),
				array(
					'name'      => esc_html__('Gigantic', 'twentytwentyone'),
					'shortName' => esc_html_x('XXXL', 'Font size', 'twentytwentyone'),
					'size'      => 144,
					'slug'      => 'gigantic',
				),
			)
		);

		// Custom background color.
		add_theme_support(
			'custom-background',
			array(
				'default-color' => 'd1e4dd',
			)
		);

		// Editor color palette.
		$black     = '#000000';
		$dark_gray = '#28303D';
		$gray      = '#39414D';
		$green     = '#D1E4DD';
		$blue      = '#D1DFE4';
		$purple    = '#D1D1E4';
		$red       = '#E4D1D1';
		$orange    = '#E4DAD1';
		$yellow    = '#EEEADD';
		$white     = '#FFFFFF';

		add_theme_support(
			'editor-color-palette',
			array(
				array(
					'name'  => esc_html__('Black', 'twentytwentyone'),
					'slug'  => 'black',
					'color' => $black,
				),
				array(
					'name'  => esc_html__('Dark gray', 'twentytwentyone'),
					'slug'  => 'dark-gray',
					'color' => $dark_gray,
				),
				array(
					'name'  => esc_html__('Gray', 'twentytwentyone'),
					'slug'  => 'gray',
					'color' => $gray,
				),
				array(
					'name'  => esc_html__('Green', 'twentytwentyone'),
					'slug'  => 'green',
					'color' => $green,
				),
				array(
					'name'  => esc_html__('Blue', 'twentytwentyone'),
					'slug'  => 'blue',
					'color' => $blue,
				),
				array(
					'name'  => esc_html__('Purple', 'twentytwentyone'),
					'slug'  => 'purple',
					'color' => $purple,
				),
				array(
					'name'  => esc_html__('Red', 'twentytwentyone'),
					'slug'  => 'red',
					'color' => $red,
				),
				array(
					'name'  => esc_html__('Orange', 'twentytwentyone'),
					'slug'  => 'orange',
					'color' => $orange,
				),
				array(
					'name'  => esc_html__('Yellow', 'twentytwentyone'),
					'slug'  => 'yellow',
					'color' => $yellow,
				),
				array(
					'name'  => esc_html__('White', 'twentytwentyone'),
					'slug'  => 'white',
					'color' => $white,
				),
			)
		);

		add_theme_support(
			'editor-gradient-presets',
			array(
				array(
					'name'     => esc_html__('Purple to yellow', 'twentytwentyone'),
					'gradient' => 'linear-gradient(160deg, ' . $purple . ' 0%, ' . $yellow . ' 100%)',
					'slug'     => 'purple-to-yellow',
				),
				array(
					'name'     => esc_html__('Yellow to purple', 'twentytwentyone'),
					'gradient' => 'linear-gradient(160deg, ' . $yellow . ' 0%, ' . $purple . ' 100%)',
					'slug'     => 'yellow-to-purple',
				),
				array(
					'name'     => esc_html__('Green to yellow', 'twentytwentyone'),
					'gradient' => 'linear-gradient(160deg, ' . $green . ' 0%, ' . $yellow . ' 100%)',
					'slug'     => 'green-to-yellow',
				),
				array(
					'name'     => esc_html__('Yellow to green', 'twentytwentyone'),
					'gradient' => 'linear-gradient(160deg, ' . $yellow . ' 0%, ' . $green . ' 100%)',
					'slug'     => 'yellow-to-green',
				),
				array(
					'name'     => esc_html__('Red to yellow', 'twentytwentyone'),
					'gradient' => 'linear-gradient(160deg, ' . $red . ' 0%, ' . $yellow . ' 100%)',
					'slug'     => 'red-to-yellow',
				),
				array(
					'name'     => esc_html__('Yellow to red', 'twentytwentyone'),
					'gradient' => 'linear-gradient(160deg, ' . $yellow . ' 0%, ' . $red . ' 100%)',
					'slug'     => 'yellow-to-red',
				),
				array(
					'name'     => esc_html__('Purple to red', 'twentytwentyone'),
					'gradient' => 'linear-gradient(160deg, ' . $purple . ' 0%, ' . $red . ' 100%)',
					'slug'     => 'purple-to-red',
				),
				array(
					'name'     => esc_html__('Red to purple', 'twentytwentyone'),
					'gradient' => 'linear-gradient(160deg, ' . $red . ' 0%, ' . $purple . ' 100%)',
					'slug'     => 'red-to-purple',
				),
			)
		);

		/*
		* Adds starter content to highlight the theme on fresh sites.
		* This is done conditionally to avoid loading the starter content on every
		* page load, as it is a one-off operation only needed once in the customizer.
		*/
		if (is_customize_preview()) {
			require get_template_directory() . '/inc/starter-content.php';
			add_theme_support('starter-content', twenty_twenty_one_get_starter_content());
		}

		// Add support for responsive embedded content.
		add_theme_support('responsive-embeds');

		// Add support for custom line height controls.
		add_theme_support('custom-line-height');

		// Add support for experimental link color control.
		add_theme_support('experimental-link-color');

		// Add support for experimental cover block spacing.
		add_theme_support('custom-spacing');

		// Add support for custom units.
		// This was removed in WordPress 5.6 but is still required to properly support WP 5.5.
		add_theme_support('custom-units');

		// Remove feed icon link from legacy RSS widget.
		add_filter('rss_widget_feed_link', '__return_false');
	}
}
add_action('after_setup_theme', 'twenty_twenty_one_setup');

/**
 * Register widget area.
 *
 * @since Twenty Twenty-One 1.0
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 *
 * @return void
 */
function twenty_twenty_one_widgets_init()
{

	register_sidebar(
		array(
			'name'          => esc_html__('Footer', 'twentytwentyone'),
			'id'            => 'sidebar-1',
			'description'   => esc_html__('Add widgets here to appear in your footer.', 'twentytwentyone'),
			'before_widget' => '<section id="%1$s" class="widget %2$s">',
			'after_widget'  => '</section>',
			'before_title'  => '<h2 class="widget-title">',
			'after_title'   => '</h2>',
		)
	);
}
add_action('widgets_init', 'twenty_twenty_one_widgets_init');

/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 *
 * Priority 0 to make it available to lower priority callbacks.
 *
 * @since Twenty Twenty-One 1.0
 *
 * @global int $content_width Content width.
 *
 * @return void
 */
function twenty_twenty_one_content_width()
{
	// This variable is intended to be overruled from themes.
	// Open WPCS issue: {@link https://github.com/WordPress-Coding-Standards/WordPress-Coding-Standards/issues/1043}.
	// phpcs:ignore WordPress.NamingConventions.PrefixAllGlobals.NonPrefixedVariableFound
	$GLOBALS['content_width'] = apply_filters('twenty_twenty_one_content_width', 750);
}
add_action('after_setup_theme', 'twenty_twenty_one_content_width', 0);

/**
 * Enqueue scripts and styles.
 *
 * @since Twenty Twenty-One 1.0
 *
 * @return void
 */
function twenty_twenty_one_scripts()
{
	// Note, the is_IE global variable is defined by WordPress and is used
	// to detect if the current browser is internet explorer.
	global $is_IE, $wp_scripts;
	// if ($is_IE) {
	// 	// If IE 11 or below, use a flattened stylesheet with static values replacing CSS Variables.
	// 	wp_enqueue_style('twenty-twenty-one-style', get_template_directory_uri() . '/assets/css/ie.css', array(), wp_get_theme()->get('Version'));
	// } else {
	// 	// If not IE, use the standard stylesheet.
	// 	wp_enqueue_style('twenty-twenty-one-style', get_template_directory_uri() . '/style.css', array(), wp_get_theme()->get('Version'));
	// }
	wp_enqueue_style('twenty-twenty-one-style', get_stylesheet_uri(), array(), filemtime(get_stylesheet_directory() . '/style.css'));
	wp_enqueue_style('twenty-twenty-one-media', get_stylesheet_directory_uri() . '/assets/css/media.css', array(), filemtime(get_stylesheet_directory() . '/assets/css/media.css'));

	// RTL styles.
	wp_style_add_data('twenty-twenty-one-style', 'rtl', 'replace');

	// Print styles.
	wp_enqueue_style('twenty-twenty-one-print-style', get_template_directory_uri() . '/assets/css/print.css', array(), wp_get_theme()->get('Version'), 'print');

	// Threaded comment reply styles.
	if (is_singular() && comments_open() && get_option('thread_comments')) {
		wp_enqueue_script('comment-reply');
	}

	// Register the IE11 polyfill file.
	wp_register_script(
		'twenty-twenty-one-ie11-polyfills-asset',
		get_template_directory_uri() . '/assets/js/polyfills.js',
		array(),
		wp_get_theme()->get('Version'),
		true
	);

	// Register the IE11 polyfill loader.
	wp_register_script(
		'twenty-twenty-one-ie11-polyfills',
		null,
		array(),
		wp_get_theme()->get('Version'),
		true
	);
	wp_add_inline_script(
		'twenty-twenty-one-ie11-polyfills',
		wp_get_script_polyfill(
			$wp_scripts,
			array(
				'Element.prototype.matches && Element.prototype.closest && window.NodeList && NodeList.prototype.forEach' => 'twenty-twenty-one-ie11-polyfills-asset',
			)
		)
	);

	// Main navigation scripts.
	if (has_nav_menu('primary')) {
		wp_enqueue_script(
			'twenty-twenty-one-primary-navigation-script',
			get_template_directory_uri() . '/assets/js/primary-navigation.js',
			array('twenty-twenty-one-ie11-polyfills'),
			wp_get_theme()->get('Version'),
			true
		);
	}

	// Responsive embeds script.
	wp_enqueue_script(
		'twenty-twenty-one-responsive-embeds-script',
		get_template_directory_uri() . '/assets/js/responsive-embeds.js',
		array('twenty-twenty-one-ie11-polyfills'),
		wp_get_theme()->get('Version'),
		true
	);
}
add_action('wp_enqueue_scripts', 'twenty_twenty_one_scripts');

/**
 * Enqueue block editor script.
 *
 * @since Twenty Twenty-One 1.0
 *
 * @return void
 */
function twentytwentyone_block_editor_script()
{

	wp_enqueue_script('twentytwentyone-editor', get_theme_file_uri('/assets/js/editor.js'), array('wp-blocks', 'wp-dom'), wp_get_theme()->get('Version'), true);
}

add_action('enqueue_block_editor_assets', 'twentytwentyone_block_editor_script');

/**
 * Fix skip link focus in IE11.
 *
 * This does not enqueue the script because it is tiny and because it is only for IE11,
 * thus it does not warrant having an entire dedicated blocking script being loaded.
 *
 * @since Twenty Twenty-One 1.0
 *
 * @link https://git.io/vWdr2
 */
function twenty_twenty_one_skip_link_focus_fix()
{

	// If SCRIPT_DEBUG is defined and true, print the unminified file.
	if (defined('SCRIPT_DEBUG') && SCRIPT_DEBUG) {
		echo '<script>';
		include get_template_directory() . '/assets/js/skip-link-focus-fix.js';
		echo '</script>';
	} else {
		// The following is minified via `npx terser --compress --mangle -- assets/js/skip-link-focus-fix.js`.
?>
		<script>
			/(trident|msie)/i.test(navigator.userAgent) && document.getElementById && window.addEventListener && window.addEventListener("hashchange", (function() {
				var t, e = location.hash.substring(1);
				/^[A-z0-9_-]+$/.test(e) && (t = document.getElementById(e)) && (/^(?:a|select|input|button|textarea)$/i.test(t.tagName) || (t.tabIndex = -1), t.focus())
			}), !1);
		</script>
	<?php
	}
}
add_action('wp_print_footer_scripts', 'twenty_twenty_one_skip_link_focus_fix');

/**
 * Enqueue non-latin language styles.
 *
 * @since Twenty Twenty-One 1.0
 *
 * @return void
 */
function twenty_twenty_one_non_latin_languages()
{
	$custom_css = twenty_twenty_one_get_non_latin_css('front-end');

	if ($custom_css) {
		wp_add_inline_style('twenty-twenty-one-style', $custom_css);
	}
}
add_action('wp_enqueue_scripts', 'twenty_twenty_one_non_latin_languages');

// SVG Icons class.
require get_template_directory() . '/classes/class-twenty-twenty-one-svg-icons.php';

// Custom color classes.
require get_template_directory() . '/classes/class-twenty-twenty-one-custom-colors.php';
new Twenty_Twenty_One_Custom_Colors();

// Enhance the theme by hooking into WordPress.
require get_template_directory() . '/inc/template-functions.php';

// Menu functions and filters.
require get_template_directory() . '/inc/menu-functions.php';

// Custom template tags for the theme.
require get_template_directory() . '/inc/template-tags.php';

// Customizer additions.
require get_template_directory() . '/classes/class-twenty-twenty-one-customize.php';
new Twenty_Twenty_One_Customize();

// Block Patterns.
require get_template_directory() . '/inc/block-patterns.php';

// Block Styles.
require get_template_directory() . '/inc/block-styles.php';

// Gallery
require_once(get_template_directory() . '/gallery/gallery.php');

// Dark Mode.
require_once get_template_directory() . '/classes/class-twenty-twenty-one-dark-mode.php';
new Twenty_Twenty_One_Dark_Mode();

/**
 * Enqueue scripts for the customizer preview.
 *
 * @since Twenty Twenty-One 1.0
 *
 * @return void
 */
function twentytwentyone_customize_preview_init()
{
	wp_enqueue_script(
		'twentytwentyone-customize-helpers',
		get_theme_file_uri('/assets/js/customize-helpers.js'),
		array(),
		wp_get_theme()->get('Version'),
		true
	);

	wp_enqueue_script(
		'twentytwentyone-customize-preview',
		get_theme_file_uri('/assets/js/customize-preview.js'),
		array('customize-preview', 'customize-selective-refresh', 'jquery', 'twentytwentyone-customize-helpers'),
		wp_get_theme()->get('Version'),
		true
	);
}
add_action('customize_preview_init', 'twentytwentyone_customize_preview_init');

/**
 * Enqueue scripts for the customizer.
 *
 * @since Twenty Twenty-One 1.0
 *
 * @return void
 */
function twentytwentyone_customize_controls_enqueue_scripts()
{

	wp_enqueue_script(
		'twentytwentyone-customize-helpers',
		get_theme_file_uri('/assets/js/customize-helpers.js'),
		array(),
		wp_get_theme()->get('Version'),
		true
	);
}
add_action('customize_controls_enqueue_scripts', 'twentytwentyone_customize_controls_enqueue_scripts');

/**
 * Calculate classes for the main <html> element.
 *
 * @since Twenty Twenty-One 1.0
 *
 * @return void
 */
function twentytwentyone_the_html_classes()
{
	/**
	 * Filters the classes for the main <html> element.
	 *
	 * @since Twenty Twenty-One 1.0
	 *
	 * @param string The list of classes. Default empty string.
	 */
	$classes = apply_filters('twentytwentyone_html_classes', '');
	if (! $classes) {
		return;
	}
	echo 'class="' . esc_attr($classes) . '"';
}

/**
 * Add "is-IE" class to body if the user is on Internet Explorer.
 *
 * @since Twenty Twenty-One 1.0
 *
 * @return void
 */
function twentytwentyone_add_ie_class()
{
	?>
	<script>
		if (-1 !== navigator.userAgent.indexOf('MSIE') || -1 !== navigator.appVersion.indexOf('Trident/')) {
			document.body.classList.add('is-IE');
		}
	</script>
<?php
}
add_action('wp_footer', 'twentytwentyone_add_ie_class');

if (! function_exists('wp_get_list_item_separator')) :
	/**
	 * Retrieves the list item separator based on the locale.
	 *
	 * Added for backward compatibility to support pre-6.0.0 WordPress versions.
	 *
	 * @since 6.0.0
	 */
	function wp_get_list_item_separator()
	{
		/* translators: Used between list items, there is a space after the comma. */
		return __(', ', 'twentytwentyone');
	}
endif;


function my_custom_post_testimonial()
{
	$labels = array(
		'name'               => _x('Testimonial', 'post type general name'),
		'singular_name'      => _x('Testimonial', 'post type singular name'),
		'add_new'            => _x('Add New', 'book'),
		'add_new_item'       => __('Add New'),
		'edit_item'          => __('Edit Testimonial'),
		'all_items'          => __('All Testimonials'),
		'view_item'          => __('View Testimonial'),
		'search_items'       => __('Search Testimonial'),
		'not_found'          => __('No Testimonial found'),
		'not_found_in_trash' => __('No Testimonial found in the Trash'),
		'parent_item_colon'  => '',
		'menu_name'          => 'Testimonials',
		'rewrite' => array('slug' => '/')
	);
	$args = array(
		'labels'        => $labels,
		'taxonomies'    => array('category'),
		'description'   => 'Holds Testimonials on the website',
		'public'        => true,
		// turn off permalinks code start
		'exclude_from_search' => true,
		'show_in_admin_bar' => false,
		'show_in_nav_menus' => false,
		'publicly_queryable' => false,
		// turn off permalinks code end
		'menu_position' => 23,
		'supports'      => array('title', 'editor', 'thumbnail'),
		'has_archive'   => true,
		'menu_icon'     => 'dashicons-images-alt2'
	);
	register_post_type('testimonial', $args);
}
add_action('init', 'my_custom_post_testimonial');


function register_custom_post_team()
{
	$labels = array(
		'name'               => _x('Team', 'post type general name'),
		'singular_name'      => _x('Team Member', 'post type singular name'),
		'add_new'            => _x('Add New', 'team member'),
		'add_new_item'       => __('Add New Team Member'),
		'edit_item'          => __('Edit Team Member'),
		'new_item'           => __('New Team Member'),
		'all_items'          => __('All Team Members'),
		'view_item'          => __('View Team Member'),
		'search_items'       => __('Search Team Members'),
		'not_found'          => __('No Team Members found'),
		'not_found_in_trash' => __('No Team Members found in Trash'),
		'menu_name'          => 'Team'
	);

	$args = array(
		'labels'             => $labels,
		'public'             => true,
		'exclude_from_search' => true,
		'show_in_admin_bar'  => false,
		'show_in_nav_menus'  => false,
		'publicly_queryable' => false,
		'menu_position'      => 23,
		'menu_icon'          => 'dashicons-groups',
		'supports'           => array('title', 'editor', 'thumbnail'),
		'rewrite'            => array('slug' => 'team'),
		'taxonomies'         => array('category')
	);

	register_post_type('team', $args);
}
add_action('init', 'register_custom_post_team');


function my_custom_post_services()
{
	$labels = array(
		'name'               => _x('Services', 'post type general name'),
		'singular_name'      => _x('Services', 'post type singular name'),
		'add_new'            => _x('Add New', 'book'),
		'add_new_item'       => __('Add New'),
		'edit_item'          => __('Edit Service'),
		'all_items'          => __('All Services'),
		'view_item'          => __('View Service'),
		'search_items'       => __('Search Services'),
		'not_found'          => __('No Services found'),
		'not_found_in_trash' => __('No Services found in the Trash'),
		'parent_item_colon'  => '',
		'menu_name'          => 'Services',
		'rewrite' => array('slug' => '/')
	);
	$args = array(
		'labels'        => $labels,
		'taxonomies'    => array('category'),
		'description'   => 'Holds Services on the website',
		'public'        => true,
		// turn off permalinks code start
		'exclude_from_search' => true,
		'show_in_admin_bar' => false,
		'show_in_nav_menus' => false,
		'publicly_queryable' => false,
		// turn off permalinks code end
		'menu_position' => 23,
		'supports'      => array('title', 'editor', 'thumbnail'),
		'has_archive'   => true,
		'menu_icon'     => 'dashicons-images-alt2'
	);
	register_post_type('services', $args);
}
add_action('init', 'my_custom_post_services');


function my_custom_post_transactions()
{
	$labels = array(
		'name'               => _x('Transactions', 'post type general name'),
		'singular_name'      => _x('Transactions', 'post type singular name'),
		'add_new'            => _x('Add New', 'book'),
		'add_new_item'       => __('Add New'),
		'edit_item'          => __('Edit Transaction'),
		'all_items'          => __('All Transactions'),
		'view_item'          => __('View Transaction'),
		'search_items'       => __('Search Transactions'),
		'not_found'          => __('No Transactions found'),
		'not_found_in_trash' => __('No Transactions found in the Trash'),
		'parent_item_colon'  => '',
		'menu_name'          => 'Transactions',
		'rewrite' => array('slug' => '/')
	);
	$args = array(
		'labels'        => $labels,
		'taxonomies'    => array('category'),
		'description'   => 'Holds Transactions on the website',
		'public'        => true,
		// turn off permalinks code start
		'exclude_from_search' => true,
		'show_in_admin_bar' => false,
		'show_in_nav_menus' => false,
		'publicly_queryable' => false,
		// turn off permalinks code end
		'menu_position' => 23,
		'supports'      => array('title', 'editor', 'thumbnail'),
		'has_archive'   => true,
		'menu_icon'     => 'dashicons-images-alt2'
	);
	register_post_type('transactions', $args);
}
add_action('init', 'my_custom_post_transactions');


function my_custom_post_listings()
{
	$labels = array(
		'name'               => _x('Listings', 'post type general name'),
		'singular_name'      => _x('Listings', 'post type singular name'),
		'add_new'            => _x('Add New', 'book'),
		'add_new_item'       => __('Add New'),
		'edit_item'          => __('Edit Listing'),
		'all_items'          => __('All Listings'),
		'view_item'          => __('View Listing'),
		'search_items'       => __('Search Listings'),
		'not_found'          => __('No Listings found'),
		'not_found_in_trash' => __('No Listings found in the Trash'),
		'parent_item_colon'  => '',
		'menu_name'          => 'Listings',
		'rewrite' => array('slug' => '/')
	);
	$args = array(
		'labels'        => $labels,
		// 'taxonomies'    => array('category'),
		'description'   => 'Holds Listings on the website',
		'public'        => true,
		// turn off permalinks code start
		'exclude_from_search' => true,
		'show_in_admin_bar' => false,
		'show_in_nav_menus' => false,
		'publicly_queryable' => false,
		// turn off permalinks code end
		'menu_position' => 23,
		'supports'      => array('title', 'editor', 'thumbnail'),
		'has_archive'   => true,
		'menu_icon'     => 'dashicons-images-alt2'
	);
	register_post_type('listings', $args);
}
add_action('init', 'my_custom_post_listings');

function register_custom_post_slider()
{
	$labels = array(
		'name'               => _x('Sliders', 'post type general name'),
		'singular_name'      => _x('Slide', 'post type singular name'),
		'add_new'            => _x('Add New', 'slider'),
		'add_new_item'       => __('Add New Slide'),
		'edit_item'          => __('Edit Slide'),
		'new_item'           => __('New Slide'),
		'all_items'          => __('All Slides'),
		'view_item'          => __('View Slide'),
		'search_items'       => __('Search Slides'),
		'not_found'          => __('No slides found'),
		'not_found_in_trash' => __('No slides found in Trash'),
		'menu_name'          => 'Image Slider'
	);

	$args = array(
		'labels'              => $labels,
		'public'              => true,
		'publicly_queryable'  => false,
		'exclude_from_search' => true,
		'show_in_admin_bar'   => false,
		'show_in_nav_menus'   => false,
		'supports'            => array('title', 'thumbnail'),
		'menu_position'       => 24,
		'menu_icon'           => 'dashicons-images-alt2',
		'has_archive'         => false,
		'rewrite'             => false,
	);

	register_post_type('slider', $args);
}
add_action('init', 'register_custom_post_slider');

/**
 * Enqueue AJAX script and localize script for WordPress AJAX
 */
function enqueue_customize_form_ajax_script() {
    wp_enqueue_script('jquery');
    wp_enqueue_script(
        'customize-form-ajax',
        get_template_directory_uri() . '/assets/js/main.js',
        array('jquery'),
        wp_get_theme()->get('Version'),
        true
    );

    // Localize script to pass AJAX URL and nonce
    wp_localize_script('customize-form-ajax', 'customize_form_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('customize_form_nonce'),
    ));
}
add_action('wp_enqueue_scripts', 'enqueue_customize_form_ajax_script');

/**
 * Handle AJAX form submission for customization form
 */
function handle_customize_form_submission() {
    // Check if this is a valid AJAX request
    if (!defined('DOING_AJAX') || !DOING_AJAX) {
        wp_die('Invalid request');
    }

    // Verify nonce for security - check both the AJAX nonce and form nonce
    $ajax_nonce_valid = wp_verify_nonce($_POST['nonce'] ?? '', 'customize_form_nonce');
    $form_nonce_valid = wp_verify_nonce($_POST['customize_form_nonce_field'] ?? '', 'customize_form_nonce');

    if (!$ajax_nonce_valid && !$form_nonce_valid) {
        wp_send_json_error(array('message' => 'Security check failed. Please refresh the page and try again.'));
        return;
    }

    // Sanitize and validate form data
    $form_data = array();

    // Basic contact information
    $form_data['name'] = sanitize_text_field($_POST['name'] ?? '');
    $form_data['email'] = sanitize_email($_POST['email'] ?? '');

    // Color selections
    $form_data['primary_color'] = sanitize_hex_color($_POST['primary_color'] ?? '#092a63');
    $form_data['secondary_color'] = sanitize_hex_color($_POST['secondary_color'] ?? '#51cb3f');
    $form_data['team_overlay_color'] = sanitize_hex_color($_POST['team_overlay_color'] ?? '#072961');

    // Theme and banner options
    $form_data['theme'] = sanitize_text_field($_POST['theme'] ?? 'light');
    $form_data['main_banner'] = sanitize_text_field($_POST['main_banner'] ?? 'Option 1');

    // Team headshots
    $form_data['headshot_style'] = sanitize_text_field($_POST['headshot_style'] ?? 'Black & White');

    // Transaction settings
    $form_data['transaction_image_style'] = sanitize_text_field($_POST['transaction_image_style'] ?? 'Black & White');
    $form_data['transaction_image_hover_style'] = sanitize_text_field($_POST['transaction_image_hover_style'] ?? 'Colored');

    // Section visibility options
    $sections = array(
        'aboutSection', 'scrollingNumbers', 'clientLogos', 'servicesSection',
        'listingsSection', 'leadershipSection', 'transactionsSection',
        'whatsHappeningSection', 'testimonialsSection', 'testimonialsProgressBar',
        'newslettersSection'
    );

    foreach ($sections as $section) {
        $form_data[$section] = sanitize_text_field($_POST[$section] ?? 'on');
    }

    // Validate required fields
    if (empty($form_data['name']) || empty($form_data['email'])) {
        wp_send_json_error(array('message' => 'Name and email are required fields.'));
        return;
    }

    if (!is_email($form_data['email'])) {
        wp_send_json_error(array('message' => 'Please enter a valid email address.'));
        return;
    }

    // Prepare email content
    $to = get_option('admin_email'); // Send to site admin email
    $subject = 'New Theme Customization Request from ' . $form_data['name'];

    // Build email message
    $message = "A new theme customization request has been submitted.\n\n";
    $message .= "Contact Information:\n";
    $message .= "Name: " . $form_data['name'] . "\n";
    $message .= "Email: " . $form_data['email'] . "\n\n";

    $message .= "Color Preferences:\n";
    $message .= "Primary Color: " . $form_data['primary_color'] . "\n";
    $message .= "Secondary Color: " . $form_data['secondary_color'] . "\n";
    $message .= "Team Overlay Color: " . $form_data['team_overlay_color'] . "\n\n";

    $message .= "Theme Settings:\n";
    $message .= "Theme: " . $form_data['theme'] . "\n";
    $message .= "Main Banner: " . $form_data['main_banner'] . "\n";
    $message .= "Headshot Style: " . $form_data['headshot_style'] . "\n";
    $message .= "Transaction Image Style: " . $form_data['transaction_image_style'] . "\n";
    $message .= "Transaction Image Hover Style: " . $form_data['transaction_image_hover_style'] . "\n\n";

    $message .= "Section Visibility:\n";
    foreach ($sections as $section) {
        $section_name = ucwords(str_replace(array('Section', 'ProgressBar'), array(' Section', ' Progress Bar'), $section));
        $message .= $section_name . ": " . ucfirst($form_data[$section]) . "\n";
    }

    $message .= "\n---\n";
    $message .= "This email was sent from the theme customization form on " . get_bloginfo('name') . " (" . home_url() . ")";

    // Email headers
    $headers = array(
        'Content-Type: text/plain; charset=UTF-8',
        'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>',
        'Reply-To: ' . $form_data['name'] . ' <' . $form_data['email'] . '>'
    );

    // Send email
    $email_sent = wp_mail($to, $subject, $message, $headers);

    if ($email_sent) {
        wp_send_json_success(array(
            'message' => 'Thank you! Your customization request has been sent successfully. We will get in touch with you soon.'
        ));
    } else {
        wp_send_json_error(array(
            'message' => 'Sorry, there was an error sending your request. Please try again or contact us directly.'
        ));
    }
}

// Hook for both logged-in and non-logged-in users
add_action('wp_ajax_submit_customize_form', 'handle_customize_form_submission');
add_action('wp_ajax_nopriv_submit_customize_form', 'handle_customize_form_submission');

/**
 * ========================================================================
 * WORDPRESS ADMIN THEME OPTIONS PANEL
 * ========================================================================
 */

/**
 * Add theme options menu to WordPress admin
 */
function fcre_add_theme_options_menu() {
    add_theme_page(
        'Theme Options',           // Page title
        'Theme Options',           // Menu title
        'manage_options',          // Capability
        'fcre-theme-options',      // Menu slug
        'fcre_theme_options_page'  // Callback function
    );
}
add_action('admin_menu', 'fcre_add_theme_options_menu');

/**
 * Register theme options settings
 */
function fcre_register_theme_options() {
    // Register settings group
    register_setting('fcre_theme_options_group', 'fcre_theme_options', 'fcre_sanitize_theme_options');

    // Color Settings Section
    add_settings_section(
        'fcre_color_settings',
        'Color Settings',
        'fcre_color_settings_callback',
        'fcre-theme-options'
    );

    // Theme Settings Section
    add_settings_section(
        'fcre_theme_settings',
        'Theme Settings',
        'fcre_theme_settings_callback',
        'fcre-theme-options'
    );

    // Section Visibility Section
    add_settings_section(
        'fcre_section_visibility',
        'Section Visibility',
        'fcre_section_visibility_callback',
        'fcre-theme-options'
    );

    // Color Fields
    add_settings_field(
        'primary_color',
        'Primary Color',
        'fcre_color_field_callback',
        'fcre-theme-options',
        'fcre_color_settings',
        array('field' => 'primary_color', 'default' => '#092a63')
    );

    add_settings_field(
        'secondary_color',
        'Secondary Color',
        'fcre_color_field_callback',
        'fcre-theme-options',
        'fcre_color_settings',
        array('field' => 'secondary_color', 'default' => '#51cb3f')
    );

    add_settings_field(
        'team_overlay_color',
        'Team Overlay Color',
        'fcre_color_field_callback',
        'fcre-theme-options',
        'fcre_color_settings',
        array('field' => 'team_overlay_color', 'default' => '#072961')
    );

    // Theme Fields
    add_settings_field(
        'theme',
        'Theme Style',
        'fcre_radio_field_callback',
        'fcre-theme-options',
        'fcre_theme_settings',
        array(
            'field' => 'theme',
            'options' => array('light' => 'Light', 'dark' => 'Dark'),
            'default' => 'light'
        )
    );

    add_settings_field(
        'main_banner',
        'Main Banner Style',
        'fcre_radio_field_callback',
        'fcre-theme-options',
        'fcre_theme_settings',
        array(
            'field' => 'main_banner',
            'options' => array(
                'Option 1' => 'Option 1 - Background video with shapes and image',
                'Option 2' => 'Option 2 - Only background video',
                'Option 3' => 'Option 3 - Image slider'
            ),
            'default' => 'Option 1'
        )
    );

    add_settings_field(
        'headshot_style',
        'Team Headshot Style',
        'fcre_radio_field_callback',
        'fcre-theme-options',
        'fcre_theme_settings',
        array(
            'field' => 'headshot_style',
            'options' => array('Colored' => 'Colored', 'Black & White' => 'Black & White'),
            'default' => 'Black & White'
        )
    );

    add_settings_field(
        'transaction_image_style',
        'Transaction Image Style',
        'fcre_radio_field_callback',
        'fcre-theme-options',
        'fcre_theme_settings',
        array(
            'field' => 'transaction_image_style',
            'options' => array('Colored' => 'Colored', 'Black & White' => 'Black & White'),
            'default' => 'Black & White'
        )
    );

    add_settings_field(
        'transaction_image_hover_style',
        'Transaction Image Hover Style',
        'fcre_radio_field_callback',
        'fcre-theme-options',
        'fcre_theme_settings',
        array(
            'field' => 'transaction_image_hover_style',
            'options' => array('Colored' => 'Colored', 'Black & White' => 'Black & White'),
            'default' => 'Colored'
        )
    );

    // Section Visibility Fields
    $sections = array(
        'aboutSection' => 'About Section',
        'scrollingNumbers' => 'Scrolling Numbers',
        'clientLogos' => 'Client Logos',
        'servicesSection' => 'Services Section',
        'listingsSection' => 'Listings Section',
        'leadershipSection' => 'Leadership Section',
        'transactionsSection' => 'Transactions Section',
        'whatsHappeningSection' => 'What\'s Happening Section',
        'testimonialsSection' => 'Testimonials Section',
        'testimonialsProgressBar' => 'Testimonials Progress Bar',
        'newslettersSection' => 'Newsletters Section'
    );

    foreach ($sections as $key => $label) {
        add_settings_field(
            $key,
            $label,
            'fcre_toggle_field_callback',
            'fcre-theme-options',
            'fcre_section_visibility',
            array('field' => $key, 'default' => 'on')
        );
    }
}
add_action('admin_init', 'fcre_register_theme_options');

/**
 * Section callback functions
 */
function fcre_color_settings_callback() {
    echo '<p>Configure the color scheme for your theme.</p>';
}

function fcre_theme_settings_callback() {
    echo '<p>Configure the general theme settings and styles.</p>';
}

function fcre_section_visibility_callback() {
    echo '<p>Control which sections are displayed on your landing page.</p>';
}

/**
 * Field callback functions
 */
function fcre_color_field_callback($args) {
    $options = get_option('fcre_theme_options');
    $value = isset($options[$args['field']]) ? $options[$args['field']] : $args['default'];

    echo '<input type="color" id="' . $args['field'] . '" name="fcre_theme_options[' . $args['field'] . ']" value="' . esc_attr($value) . '" />';
    echo '<p class="description">Default: ' . $args['default'] . '</p>';
}

function fcre_radio_field_callback($args) {
    $options = get_option('fcre_theme_options');
    $value = isset($options[$args['field']]) ? $options[$args['field']] : $args['default'];

    echo '<fieldset>';
    foreach ($args['options'] as $option_value => $option_label) {
        $checked = checked($value, $option_value, false);
        echo '<label><input type="radio" name="fcre_theme_options[' . $args['field'] . ']" value="' . esc_attr($option_value) . '" ' . $checked . ' /> ' . esc_html($option_label) . '</label><br>';
    }
    echo '</fieldset>';
}

function fcre_toggle_field_callback($args) {
    $options = get_option('fcre_theme_options');
    $value = isset($options[$args['field']]) ? $options[$args['field']] : $args['default'];

    echo '<fieldset>';
    echo '<label><input type="radio" name="fcre_theme_options[' . $args['field'] . ']" value="on" ' . checked($value, 'on', false) . ' /> On</label><br>';
    echo '<label><input type="radio" name="fcre_theme_options[' . $args['field'] . ']" value="off" ' . checked($value, 'off', false) . ' /> Off</label>';
    echo '</fieldset>';
}

/**
 * Theme options page HTML
 */
function fcre_theme_options_page() {
    ?>
    <div class="wrap">
        <h1>Theme Options</h1>
        <p>Configure your theme settings below. These options will control the appearance and functionality of your website.</p>

        <?php settings_errors(); ?>

        <form method="post" action="options.php">
            <?php
            settings_fields('fcre_theme_options_group');
            do_settings_sections('fcre-theme-options');
            submit_button('Save Settings');
            ?>
        </form>

        <div class="fcre-admin-info" style="margin-top: 30px; padding: 20px; background: #f1f1f1; border-radius: 5px;">
            <h3>How to Use</h3>
            <p><strong>Color Settings:</strong> Use the color pickers to customize your theme's color scheme.</p>
            <p><strong>Theme Settings:</strong> Choose between different layout and style options.</p>
            <p><strong>Section Visibility:</strong> Control which sections appear on your landing page.</p>
            <p><em>Note: Changes will take effect immediately after saving. The front-end customizer will continue to work for testing purposes.</em></p>
        </div>

        <div class="fcre-export-import" style="margin-top: 30px; padding: 20px; background: #fff; border: 1px solid #ccd0d4; border-radius: 5px;">
            <h3>Export/Import Settings</h3>

            <div style="display: flex; gap: 30px; flex-wrap: wrap;">
                <div style="flex: 1; min-width: 300px;">
                    <h4>Export Settings</h4>
                    <p>Download your current theme options as a JSON file for backup or transfer.</p>
                    <form method="post" style="margin-top: 15px;">
                        <?php wp_nonce_field('fcre_export_options', 'fcre_export_nonce'); ?>
                        <input type="submit" name="fcre_export_options" class="button button-secondary" value="Export Theme Options" />
                    </form>
                </div>

                <div style="flex: 1; min-width: 300px;">
                    <h4>Import Settings</h4>
                    <p>Upload a previously exported JSON file to restore theme options.</p>
                    <form method="post" enctype="multipart/form-data" style="margin-top: 15px;">
                        <?php wp_nonce_field('fcre_import_options', 'fcre_import_nonce'); ?>
                        <input type="file" name="fcre_import_file" accept=".json" required style="margin-bottom: 10px;" />
                        <br>
                        <input type="submit" name="fcre_import_options" class="button button-secondary" value="Import Theme Options" />
                    </form>
                </div>
            </div>
        </div>
    </div>

    <style>
    .form-table th {
        width: 200px;
    }
    .fcre-admin-info {
        max-width: 800px;
    }
    .fcre-admin-info h3 {
        margin-top: 0;
        color: #23282d;
    }
    </style>
    <?php
}

/**
 * Sanitize theme options
 */
function fcre_sanitize_theme_options($input) {
    $sanitized = array();

    // Sanitize color fields
    $color_fields = array('primary_color', 'secondary_color', 'team_overlay_color');
    foreach ($color_fields as $field) {
        if (isset($input[$field])) {
            $sanitized[$field] = sanitize_hex_color($input[$field]);
        }
    }

    // Sanitize radio fields
    $radio_fields = array(
        'theme' => array('light', 'dark'),
        'main_banner' => array('Option 1', 'Option 2', 'Option 3'),
        'headshot_style' => array('Colored', 'Black & White'),
        'transaction_image_style' => array('Colored', 'Black & White'),
        'transaction_image_hover_style' => array('Colored', 'Black & White')
    );

    foreach ($radio_fields as $field => $allowed_values) {
        if (isset($input[$field]) && in_array($input[$field], $allowed_values)) {
            $sanitized[$field] = sanitize_text_field($input[$field]);
        }
    }

    // Sanitize section visibility fields
    $section_fields = array(
        'aboutSection', 'scrollingNumbers', 'clientLogos', 'servicesSection',
        'listingsSection', 'leadershipSection', 'transactionsSection',
        'whatsHappeningSection', 'testimonialsSection', 'testimonialsProgressBar',
        'newslettersSection'
    );

    foreach ($section_fields as $field) {
        if (isset($input[$field]) && in_array($input[$field], array('on', 'off'))) {
            $sanitized[$field] = sanitize_text_field($input[$field]);
        }
    }

    return $sanitized;
}

/**
 * Helper function to get theme option with default fallback
 */
function fcre_get_theme_option($option_name, $default = '') {
    $options = get_option('fcre_theme_options');
    return isset($options[$option_name]) ? $options[$option_name] : $default;
}

/**
 * Get all theme options with defaults
 */
function fcre_get_all_theme_options() {
    $defaults = array(
        // Colors
        'primary_color' => '#092a63',
        'secondary_color' => '#51cb3f',
        'team_overlay_color' => '#072961',

        // Theme settings
        'theme' => 'light',
        'main_banner' => 'Option 1',
        'headshot_style' => 'Black & White',
        'transaction_image_style' => 'Black & White',
        'transaction_image_hover_style' => 'Colored',

        // Section visibility
        'aboutSection' => 'on',
        'scrollingNumbers' => 'on',
        'clientLogos' => 'on',
        'servicesSection' => 'on',
        'listingsSection' => 'on',
        'leadershipSection' => 'on',
        'transactionsSection' => 'on',
        'whatsHappeningSection' => 'on',
        'testimonialsSection' => 'on',
        'testimonialsProgressBar' => 'on',
        'newslettersSection' => 'on'
    );

    $options = get_option('fcre_theme_options', array());
    return wp_parse_args($options, $defaults);
}

/**
 * Output custom CSS based on theme options
 */
function fcre_output_custom_css() {
    $options = fcre_get_all_theme_options();

    $css = '<style type="text/css" id="fcre-theme-options-css">';
    $css .= ':root {';
    $css .= '--primary: ' . esc_attr($options['primary_color']) . ';';
    $css .= '--secondary: ' . esc_attr($options['secondary_color']) . ';';
    $css .= '--overlay: ' . esc_attr($options['team_overlay_color']) . ';';
    $css .= '}';

    // Apply theme class to body
    if ($options['theme'] === 'dark') {
        $css .= 'body { --primaryColor: var(--secondary); --secondaryColor: var(--primary); }';
    }

    // Banner options
    if ($options['main_banner'] === 'Option 1') {
        $css .= '.bannerImageSliderSec { display: none; }';
        $css .= '.homeVideoSec { display: block; }';
        $css .= 'body { --banner-option: 1; }';
    } elseif ($options['main_banner'] === 'Option 2') {
        $css .= '.bannerImageSliderSec { display: none; }';
        $css .= '.homeVideoSec { display: block; }';
        $css .= '.homeBannerVectorOverlay { display: none; }';
        $css .= 'body { --banner-option: 2; }';
    } elseif ($options['main_banner'] === 'Option 3') {
        $css .= '.bannerImageSliderSec { display: block; }';
        $css .= '.homeVideoSec { display: none; }';
        $css .= 'body { --banner-option: 3; }';
    }

    // Headshot styles
    if ($options['headshot_style'] === 'Colored') {
        $css .= '.teamBox .teamBoxImg img { filter: grayscale(0); }';
    } else {
        $css .= '.teamBox .teamBoxImg img { filter: grayscale(100%); }';
    }

    // Transaction image styles
    if ($options['transaction_image_style'] === 'Colored') {
        $css .= '.transactionBox img { filter: grayscale(0); }';
    } else {
        $css .= '.transactionBox img { filter: grayscale(100%); }';
    }

    if ($options['transaction_image_hover_style'] === 'Colored') {
        $css .= '.transactionBox:hover img { filter: grayscale(0); }';
    } else {
        $css .= '.transactionBox:hover img { filter: grayscale(100%); }';
    }

    // Section visibility
    $sections_map = array(
        'aboutSection' => '#about',
        'scrollingNumbers' => '#scrollingNumbers',
        'clientLogos' => '#tenantLogos',
        'servicesSection' => '#services',
        'listingsSection' => '#listings',
        'leadershipSection' => '#leadership',
        'transactionsSection' => '#transactions',
        'whatsHappeningSection' => '#whatsHappening',
        'testimonialsSection' => '#testimonials',
        'testimonialsProgressBar' => '.testimonials-progress-bar',
        'newslettersSection' => '#newsletters'
    );

    foreach ($sections_map as $option_key => $selector) {
        if ($options[$option_key] === 'off') {
            $css .= $selector . ' { display: none !important; }';
        }
    }

    $css .= '</style>';

    echo $css;
}
add_action('wp_head', 'fcre_output_custom_css');

/**
 * Add body classes based on theme options
 */
function fcre_add_body_classes($classes) {
    $options = fcre_get_all_theme_options();

    // Add theme class
    if ($options['theme'] === 'dark') {
        $classes[] = 'darkTheme';
    }

    // Add banner option class
    $classes[] = 'bannerOption' . str_replace('Option ', '', $options['main_banner']);

    // Add headshot style class
    if ($options['headshot_style'] === 'Colored') {
        $classes[] = 'coloredHeadshots';
    } else {
        $classes[] = 'bwHeadshots';
    }

    return $classes;
}
add_filter('body_class', 'fcre_add_body_classes');

/**
 * Enqueue admin styles for theme options page
 */
function fcre_admin_styles($hook) {
    if ($hook !== 'appearance_page_fcre-theme-options') {
        return;
    }

    wp_enqueue_style('wp-color-picker');
    wp_enqueue_script('wp-color-picker');

    // Add custom admin CSS
    $custom_css = '
    <style>
    .fcre-theme-options .form-table th {
        width: 220px;
        padding-left: 0;
    }
    .fcre-theme-options .form-table td {
        padding-left: 20px;
    }
    .fcre-theme-options input[type="color"] {
        width: 60px;
        height: 40px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
    }
    .fcre-theme-options fieldset label {
        display: block;
        margin-bottom: 8px;
        font-weight: normal;
    }
    .fcre-theme-options fieldset input[type="radio"] {
        margin-right: 8px;
    }
    .fcre-theme-options .description {
        font-style: italic;
        color: #666;
        margin-top: 5px;
    }
    .fcre-admin-info {
        background: #fff;
        border: 1px solid #ccd0d4;
        box-shadow: 0 1px 1px rgba(0,0,0,.04);
    }
    </style>';

    echo $custom_css;
}
add_action('admin_head', 'fcre_admin_styles');

/**
 * Add settings link to theme options in admin menu
 */
function fcre_add_settings_link($links) {
    $settings_link = '<a href="' . admin_url('themes.php?page=fcre-theme-options') . '">Theme Options</a>';
    array_unshift($links, $settings_link);
    return $links;
}
add_filter('theme_action_links', 'fcre_add_settings_link');

/**
 * Enqueue theme options JavaScript and localize options
 */
function fcre_enqueue_theme_options_script() {
    wp_enqueue_script(
        'fcre-theme-options',
        get_template_directory_uri() . '/assets/js/theme-options.js',
        array(),
        wp_get_theme()->get('Version'),
        true
    );

    // Localize theme options for JavaScript
    wp_localize_script('fcre-theme-options', 'fcre_theme_options', fcre_get_all_theme_options());
}
add_action('wp_enqueue_scripts', 'fcre_enqueue_theme_options_script');

/**
 * Add admin notice for theme options
 */
function fcre_admin_notice() {
    $screen = get_current_screen();
    if ($screen->id === 'appearance_page_fcre-theme-options') {
        echo '<div class="notice notice-info"><p><strong>Theme Options:</strong> Changes will be applied immediately to your website. You can preview changes by visiting your site in a new tab.</p></div>';
    }
}
add_action('admin_notices', 'fcre_admin_notice');

/**
 * Export/Import functionality for theme options
 */
function fcre_handle_export_import() {
    if (!current_user_can('manage_options')) {
        return;
    }

    // Handle export
    if (isset($_POST['fcre_export_options']) && wp_verify_nonce($_POST['fcre_export_nonce'], 'fcre_export_options')) {
        $options = get_option('fcre_theme_options', array());
        $export_data = json_encode($options, JSON_PRETTY_PRINT);

        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="fcre-theme-options-' . date('Y-m-d') . '.json"');
        echo $export_data;
        exit;
    }

    // Handle import
    if (isset($_POST['fcre_import_options']) && wp_verify_nonce($_POST['fcre_import_nonce'], 'fcre_import_options')) {
        if (isset($_FILES['fcre_import_file']) && $_FILES['fcre_import_file']['error'] === UPLOAD_ERR_OK) {
            $import_data = file_get_contents($_FILES['fcre_import_file']['tmp_name']);
            $options = json_decode($import_data, true);

            if ($options && is_array($options)) {
                $sanitized_options = fcre_sanitize_theme_options($options);
                update_option('fcre_theme_options', $sanitized_options);
                add_settings_error('fcre_theme_options', 'import_success', 'Theme options imported successfully!', 'updated');
            } else {
                add_settings_error('fcre_theme_options', 'import_error', 'Invalid import file format.', 'error');
            }
        }
    }
}
add_action('admin_init', 'fcre_handle_export_import');
