/**
 * Template Name: Medilab - v4.8.1
 * Template URL: https://bootstrapmade.com/medilab-free-medical-bootstrap-theme/
 * Author: BootstrapMade.com
 * License: https://bootstrapmade.com/license/
 */
(function() {
    "use strict";

    /**
     * Easy selector helper function
     */
    const select = (el, all = false) => {
        el = el.trim()
        if (all) {
            return [...document.querySelectorAll(el)]
        } else {
            return document.querySelector(el)
        }
    }

    /**
     * Easy event listener function
     */
    const on = (type, el, listener, all = false) => {
        let selectEl = select(el, all)
        if (selectEl) {
            if (all) {
                selectEl.forEach(e => e.addEventListener(type, listener))
            } else {
                selectEl.addEventListener(type, listener)
            }
        }
    }

    /**
     * Easy on scroll event listener
     */
    const onscroll = (el, listener) => {
        el.addEventListener('scroll', listener)
    }

    /**
     * Scrolls to an element with header offset
     */
    const scrollto = (el) => {
        let header = select('#header')
        let offset = header.offsetHeight

        let elementPos = select(el).offsetTop
        window.scrollTo({
            top: elementPos - offset,
            behavior: 'smooth'
        })
    }

    /**
     * Toggle .header-scrolled class to #header when page is scrolled
     */
    let selectHeader = select('#header')
    let selectTopbar = select('#topbar')
    if (selectHeader) {
        const headerScrolled = () => {
            if (window.scrollY > 100) {
                selectHeader.classList.add('header-scrolled')
                if (selectTopbar) {
                    selectTopbar.classList.add('topbar-scrolled')
                }
            } else {
                selectHeader.classList.remove('header-scrolled')
                if (selectTopbar) {
                    selectTopbar.classList.remove('topbar-scrolled')
                }
            }
        }
        window.addEventListener('load', headerScrolled)
        onscroll(document, headerScrolled)
    }

    /**
     * Mobile nav toggle
     */
    const mobileNavShow = document.querySelector('.mobile-nav-show');
    const mobileNavHide = document.querySelector('.mobile-nav-hide');

    document.querySelectorAll('.mobile-nav-toggle').forEach(el => {
        el.addEventListener('click', function(event) {
            event.preventDefault();
            mobileNavToogle();
        })
    });

    function mobileNavToogle() {
        document.querySelector('body').classList.toggle('mobile-nav-active');
        mobileNavShow.classList.toggle('d-none');
        mobileNavHide.classList.toggle('d-none');
    }

    /**
     * Toggle mobile nav dropdowns
     */
    const navDropdowns = document.querySelectorAll('.navbar .dropdown > a');

    navDropdowns.forEach(el => {
        el.addEventListener('click', function(event) {
            if (document.querySelector('.mobile-nav-active')) {
                event.preventDefault();
                this.classList.toggle('active');
                this.nextElementSibling.classList.toggle('submenu-active');

                let dropDownIndicator = this.querySelector('.submenu-indicator');
                dropDownIndicator.classList.toggle('bi-chevron-up');
                dropDownIndicator.classList.toggle('bi-chevron-down');
            }
        })
    });

    /**
     * Scrool with ofset on links with a class name .scrollto
     */
    on('click', '.scrollto', function(e) {
        if (select(this.hash)) {
            e.preventDefault()

            let navbar = select('#navbar')
            if (navbar.classList.contains('navbar-mobile')) {
                navbar.classList.remove('navbar-mobile')
                let navbarToggle = select('.mobile-nav-toggle')
                navbarToggle.classList.toggle('bi-list')
                navbarToggle.classList.toggle('bi-x')
            }
            scrollto(this.hash)
        }
    }, true)

    /**
     * Handle customize form AJAX submission
     */
    document.addEventListener('DOMContentLoaded', function() {
        const customizeForm = document.getElementById('customizeForm');

        if (customizeForm) {
            customizeForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Get form data
                const formData = new FormData(this);

                // Add AJAX action and nonce
                formData.append('action', 'submit_customize_form');
                formData.append('nonce', customize_form_ajax.nonce);

                // Get submit button and show loading state
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalBtnText = submitBtn.innerHTML;
                submitBtn.innerHTML = 'Sending...';
                submitBtn.disabled = true;

                // Remove any existing messages
                const existingMessages = document.querySelectorAll('.form-message');
                existingMessages.forEach(msg => msg.remove());

                // Send AJAX request
                fetch(customize_form_ajax.ajax_url, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    // Create message element
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'form-message alert';

                    if (data.success) {
                        messageDiv.className += ' alert-success';
                        messageDiv.innerHTML = '<i class="fas fa-check-circle"></i> ' + data.data.message;

                        // Reset form on success
                        customizeForm.reset();

                        // Reset color pickers to default values
                        document.getElementById('primaryColorPicker').value = '#092a63';
                        document.getElementById('secondaryColorPicker').value = '#51cb3f';
                        document.getElementById('teamOverlayColorPicker').value = '#072961';

                        // Reset radio buttons to default checked states
                        document.getElementById('lightThemeBtn').checked = true;
                        document.getElementById('bannerOption1').checked = true;
                        document.getElementById('headshotsBW').checked = true;
                        document.getElementById('transactionImagesBW').checked = true;
                        document.getElementById('transactionImageHoverColored').checked = true;

                        // Reset all section toggles to "on"
                        const sectionToggles = [
                            'aboutSectionOn', 'scrollingNumbersOn', 'clientLogosOn',
                            'servicesSectionOn', 'listingsSectionOn', 'leadershipSectionOn',
                            'transactionsSectionOn', 'whatsHappeningSectionOn',
                            'testimonialsSectionOn', 'testimonialsProgressBarOn', 'newslettersSectionOn'
                        ];
                        sectionToggles.forEach(toggleId => {
                            const toggle = document.getElementById(toggleId);
                            if (toggle) toggle.checked = true;
                        });

                    } else {
                        messageDiv.className += ' alert-danger';
                        messageDiv.innerHTML = '<i class="fas fa-exclamation-circle"></i> ' + data.data.message;
                    }

                    // Insert message before the form
                    customizeForm.parentNode.insertBefore(messageDiv, customizeForm);

                    // Scroll to message
                    messageDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

                    // Auto-hide success message after 5 seconds
                    if (data.success) {
                        setTimeout(() => {
                            messageDiv.style.transition = 'opacity 0.5s';
                            messageDiv.style.opacity = '0';
                            setTimeout(() => {
                                if (messageDiv.parentNode) {
                                    messageDiv.parentNode.removeChild(messageDiv);
                                }
                            }, 500);
                        }, 5000);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);

                    // Create error message
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'form-message alert alert-danger';
                    messageDiv.innerHTML = '<i class="fas fa-exclamation-circle"></i> An unexpected error occurred. Please try again.';

                    // Insert message before the form
                    customizeForm.parentNode.insertBefore(messageDiv, customizeForm);

                    // Scroll to message
                    messageDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
                })
                .finally(() => {
                    // Restore submit button
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;
                });
            });
        }
    });

})()