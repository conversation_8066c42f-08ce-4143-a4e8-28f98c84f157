<?php

/**
 * Template Name: Home Page Template
 */
get_header();
global $post;
$meta      = get_field_objects($post->ID);
$home_meta = get_field_objects(12);
?>

<?php
if (have_posts()) {
    while (have_posts()) : the_post();
        $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
?>
        <div class="modal fade customizeModal" id="customizeModal" tabindex="-1" aria-labelledby="customizeModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="container-fluid modalHeaderContainer">
                        <div class="container-lg">
                            <div class="row">
                                <div class="col-12">
                                    <div class="modal-header">
                                        <h4 class="modal-title" id="customizeModalLabel">Customize This Template</h4>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">Preview Changes</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid modalBodyContainer">
                        <div class="container-lg">
                            <form  action="#" method="post" id="customizeForm" class="customizeForm">
                            <div class="row">
                                <div class="col-12">
                                    <div class="pt-5 pb-4 modal-body" id="customizeModalContent">
                                        <div>
                                            <p><strong>Use these options to customize this theme according to your needs</strong><br />
                                                This is just a run time simulation of how this landing page can be customized, everything will reset to default if you reload this page. Once you are done with the
                                                customization, please enter your name and email address at the bottom of this screen to send us what you selected and we will get in touch with you.</p>
                                        </div>
                                        <div class="colorPicker">
                                            <div>
                                                <label for="primaryColorPicker">Primary Color:</label>
                                                <input type="color" id="primaryColorPicker" value="#092a63" name="primary_color">
                                            </div>
                                            <div>
                                                <label for="secondaryColorPicker">Secondary Color:</label>
                                                <input type="color" id="secondaryColorPicker" value="#51cb3f" name="secondary_color">
                                            </div>
                                        </div>
                                        <div>
                                            <h3>Theme:</h3>
                                            <div class="radio-group">
                                                <div class="btn-radio">
                                                        <input type="radio" name="theme" value="light" checked id="lightThemeBtn"><label for="lightThemeBtn">Light</label>
                                                </div>
                                                <div class="btn-radio">
                                                    <input type="radio" name="theme" value="dark" id="darkThemeBtn"><label for="darkThemeBtn">Dark</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mainBannerOptions">
                                            <h3>Main Banner:</h3>
                                            <div class="radio-group">
                                                <div class="btn-radio">
                                                    <input type="radio" name="main_banner" value="Option 1" checked id="bannerOption1"><label for="bannerOption1">Option 1 - Background video with shapes on the right side with an image <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/banner-option-1-video-image.jpg" /></label>
                                                </div>
                                                <div class="btn-radio">
                                                    <input type="radio" name="main_banner" value="Option 2" id="bannerOption2"><label for="bannerOption2">Option 2 - Only a background video. You can change the video from WordPress backend. <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/banner-option-2-video-only.jpg" /></label>
                                                </div>
                                                <div class="btn-radio">
                                                    <input type="radio" name="main_banner" value="Option 3" id="bannerOption3"><label for="bannerOption3">Option 3 - Image slider. You can change the images from WordPress backend. <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/banner-option-3-image-slider.jpg" /></label>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <h3>Team Headshots</h3>
                                            <div>
                                                <h4>Headshot style:</h4>
                                                <div class="radio-group">
                                                    <div class="btn-radio">
                                                         <input type="radio" name="headshot_style" value="Colored" id="headshotsColored"><label for="headshotsColored">Colored</label>
                                                    </div>
                                                    <div class="btn-radio">
                                                        <input type="radio" name="headshot_style" value="Black & White" checked id="headshotsBW"><label for="headshotsBW">Black & White</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div>
                                                <label for="teamOverlayColorPicker">Overlay Color:</label>
                                                <input type="color" id="teamOverlayColorPicker" value="#072961" name="team_overlay_color">
                                            </div>
                                        </div>
                                        <div>
                                            <h3>Transactions</h3>
                                            <div>
                                                <h4>Image style:</h4>
                                                <div class="radio-group">
                                                    <div class="btn-radio">
                                                         <input type="radio" name="transaction_image_style" value="Colored"  id="transactionImagesColored"><label for="transactionImagesColored">Colored</label>
                                                    </div>
                                                    <div class="btn-radio">
                                                        <input type="radio" name="transaction_image_style" value="Black & White" checked id="transactionImagesBW"><label for="transactionImagesBW">Black & White</label>
                                                    </div>
                                                </div>

                                            </div>
                                            <div>
                                                <h4>Image hover style:</h4>
                                                <div class="radio-group">
                                                    <div class="btn-radio">
                                                         <input type="radio" name="transaction_image_hover_style" value="Colored" checked id="transactionImageHoverColored"><label for="transactionImageHoverColored">Colored</label>
                                                    </div>
                                                    <div class="btn-radio">
                                                        <input type="radio" name="transaction_image_hover_style" value="Black & White"  id="transactionImageHoverBW"><label for="transactionImageHoverBW">Black & White</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            Use the following options to turn off the sections you don't want to display on your landing page.
                                        </div>
                                        <div>
                                            <h3>About section</h3>
                                            <div>
                                                <input type="radio" id="aboutSectionOn" name="aboutSection" value="on" checked>
                                                <label for="aboutSectionOn">On</label>
                                                <input type="radio" id="aboutSectionOff" name="aboutSection" value="off">
                                                <label for="aboutSectionOff">Off</label>

                                            </div>
                                        </div>
                                        <div>
                                            <h3>Scrolling numbers</h3>
                                            <div>
                                                <input type="radio" id="scrollingNumbersOn" name="scrollingNumbers" value="on" checked>
                                                <label for="scrollingNumbersOn">On</label>
                                                <input type="radio" id="scrollingNumbersOff" name="scrollingNumbers" value="off">
                                                <label for="scrollingNumbersOff">Off</label>

                                            </div>
                                        </div>
                                        <div>
                                            <h3>Client Logos</h3>
                                            <div>
                                                <input type="radio" id="clientLogosOn" name="clientLogos" value="on" checked>
                                                <label for="clientLogosOn">On</label>
                                                <input type="radio" id="clientLogosOff" name="clientLogos" value="off">
                                                <label for="clientLogosOff">Off</label>

                                            </div>
                                        </div>
                                        <div>
                                            <h3>Services section</h3>
                                            <div>
                                                <input type="radio" id="servicesSectionOn" name="servicesSection" value="on" checked>
                                                <label for="servicesSectionOn">On</label>
                                                <input type="radio" id="servicesSectionOff" name="servicesSection" value="off">
                                                <label for="servicesSectionOff">Off</label>

                                            </div>
                                        </div>
                                        <div>
                                            <h3>Listings section</h3>
                                            <div>
                                                <input type="radio" id="listingsSectionOn" name="listingsSection" value="on" checked>
                                                <label for="listingsSectionOn">On</label>
                                                <input type="radio" id="listingsSectionOff" name="listingsSection" value="off">
                                                <label for="listingsSectionOff">Off</label>

                                            </div>
                                        </div>
                                        <div>
                                            <h3>Leadership section</h3>
                                            <div>
                                                <input type="radio" id="leadershipSectionOn" name="leadershipSection" value="on" checked>
                                                <label for="leadershipSectionOn">On</label>
                                                <input type="radio" id="leadershipSectionOff" name="leadershipSection" value="off">
                                                <label for="leadershipSectionOff">Off</label>

                                            </div>
                                        </div>
                                        <div>
                                            <h3>Transactions section</h3>
                                            <div>
                                                <input type="radio" id="transactionsSectionOn" name="transactionsSection" value="on" checked>
                                                <label for="transactionsSectionOn">On</label>
                                                <input type="radio" id="transactionsSectionOff" name="transactionsSection" value="off">
                                                <label for="transactionsSectionOff">Off</label>

                                            </div>
                                        </div>
                                        <div>
                                            <h3>What's Happening section</h3>
                                            <div>
                                                <input type="radio" id="whatsHappeningSectionOn" name="whatsHappeningSection" value="on" checked>
                                                <label for="whatsHappeningSectionOn">On</label>
                                                <input type="radio" id="whatsHappeningSectionOff" name="whatsHappeningSection" value="off">
                                                <label for="whatsHappeningSectionOff">Off</label>

                                            </div>
                                        </div>
                                        <div>
                                            <h3>Testimonials section</h3>
                                            <div>
                                                <input type="radio" id="testimonialsSectionOn" name="testimonialsSection" value="on" checked>
                                                <label for="testimonialsSectionOn">On</label>
                                                <input type="radio" id="testimonialsSectionOff" name="testimonialsSection" value="off">
                                                <label for="testimonialsSectionOff">Off</label>

                                            </div>
                                        </div>
                                        <div>
                                            <h3>Testimonials progress bar</h3>
                                            <div>
                                                <input type="radio" id="testimonialsProgressBarOn" name="testimonialsProgressBar" value="on" checked>
                                                <label for="testimonialsProgressBarOn">On</label>
                                                <input type="radio" id="testimonialsProgressBarOff" name="testimonialsProgressBar" value="off">
                                                <label for="testimonialsProgressBarOff">Off</label>

                                            </div>
                                        </div>
                                        <div>
                                            <h3>Newsletters section</h3>
                                            <div>
                                                <input type="radio" id="newslettersSectionOn" name="newslettersSection" value="on" checked>
                                                <label for="newslettersSectionOn">On</label>
                                                <input type="radio" id="newslettersSectionOff" name="newslettersSection" value="off">
                                                <label for="newslettersSectionOff">Off</label>

                                            </div>
                                        </div>
                                        <div class="row">
                                           <div class="col-md-4">
                                                 <div class="form-group">
                                                    <input type="text" class="form-control" id="name" name="name" placeholder="Your Name">
                                                </div>
                                           </div>
                                           <div class="col-md-4">
                                             <div class="form-group">
                                                <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>">
                                            </div>
                                           </div>
                                            <div class="col-md-4">  
                                                <button type="submit" class="btn btn-primary">Submit</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="customizeBtnBoxFixed" style="display: none;">
            <a href="#" data-bs-toggle="modal" data-bs-target="#customizeModal" class="animatedShadow"><i class="fa-solid fa-pencil"></i>Customize</a>
        </div>


        <section id="home">
            <div class="container-fluid pageMargin bannerImageSliderSec">
                <div class="container-xl bannerSliderTextBox">
                    <div class="row align-items-center">
                        <div class="col-12">
                            <div>
                                <?php echo $home_meta['banner_text']['value']; ?>
                                <a href="#about" class="btnBanner">Learn More</a>
                                <div class="customizeTemplateBtnBox">
                                    <a href="#" data-bs-toggle="modal" data-bs-target="#customizeModal" class="animatedShadow"><i class="fa-solid fa-pencil"></i>Customize This Template</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="owl-carousel owl-theme bannerImageSlider">
                    <?php
                    $slider_query = new WP_Query(array(
                        'post_type' => 'slider',
                        'posts_per_page' => -1,
                        'order' => 'DESC'
                    ));

                    if ($slider_query->have_posts()) :
                        while ($slider_query->have_posts()) : $slider_query->the_post();
                    ?>
                            <div class="item bannerSliderImageBox">
                                <?php the_post_thumbnail('single-post-thumbnail', array('class' => 'img-fluid bannerSliderImg')); ?>
                            </div>
                    <?php
                        endwhile;
                        wp_reset_postdata();
                    endif;
                    ?>
                </div>
            </div>
            <div class="container-fluid pageMargin homeVideoSec">
                <div class="row">
                    <div class="col-xl-12 px-0 homeVideoSecInner">
                        <video autoplay="" muted="" loop="" class="">
                            <source src="<?php echo $home_meta['banner_video']['value']; ?>" type="video/mp4">
                        </video>
                        <div class="homeVideoSecText">
                            <div>
                                <?php echo $home_meta['banner_text']['value']; ?>
                                <a href="#about" class="btnBanner">Learn More</a>
                                <div class="customizeTemplateBtnBox">
                                    <a href="#" data-bs-toggle="modal" data-bs-target="#customizeModal" class="animatedShadow"><i class="fa-solid fa-pencil"></i>Customize This Template</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="homeBannerVectorOverlay">
                    <svg id="shapePrimaryColor" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 519.07 519.08">
                        <defs>
                            <!-- <style>
                                .cls-1 {
                                    fill: none;
                                }
                            </style> -->
                        </defs>
                        <g id="Layer_1-2" data-name="Layer 1">
                            <polygon class="cls-1" points="519.07 0 0 519.08 0 185.6 185.61 0 519.07 0" />
                        </g>
                    </svg>
                    <svg id="shapeAccentColor" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 263.63 263.63">
                        <defs>
                        </defs>
                        <g id="Layer_1-2" data-name="Layer 1">
                            <polygon class="cls-1" points="263.63 0 263.63 263.63 0 263.63 263.63 0" />
                        </g>
                    </svg>
                    <div class="shapeForImgBox">
                        <img src="<?php echo $home_meta['banner_side_image']['value']; ?>" alt="">
                        <svg id="shapeForImg" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 603.74 752.85">
                            <defs>
                            </defs>
                            <g id="Layer_1-2" data-name="Layer 1">
                                <g>
                                    <polygon class="cls-1" points="0 0 0 752.85 101.18 752.85 603.74 250.29 603.74 0 0 0" />
                                    <polygon class="cls-1" points="603.74 752.85 603.74 293.49 144.38 752.85 603.74 752.85" />
                                </g>
                            </g>
                        </svg>
                    </div>
                </div>
            </div>
        </section>
<?php
    endwhile;
}
?>

<section id="about" class="idScrollFix">
    <?php
    if (have_posts()) {
        while (have_posts()) : the_post();
            $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
    ?>
            <div class="aboutSec">
                <div class="row">
                    <div class="col-lg-5 col-md-5 col-12 position-relative">
                        <?php if (has_post_thumbnail()) { ?>
                            <?php the_post_thumbnail('single-post-thumbnail', array('class' => 'img-fluid aboutSecImg')); ?>
                        <?php } else { ?>
                            <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/hirise.webp" alt="" class="img-fluid aboutSecImg">
                        <?php } ?>
                    </div>
                    <div class="col-xxl-6 col-xl-6 col-lg-6 col-md-7 col-12 p-0">
                        <div class="py-8 aboutTextBorder"></div>
                        <div class="pb-10 ps-xxl-15 ps-12 pe-5 aboutText">

                            <?php the_content(); ?>


                            <div id="scrollingNumbers" class="aboutCounter">
                                <div class="row">
                                    <div class="col-lg-4 col-md-4 col-sm-4">
                                        <div class="counterBox">
                                            <h2><span class="counter">6.4</span><span>M</span><span>+</span></h2>
                                            <p>Square Feet</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-4 col-sm-4">
                                        <div class="counterBox">
                                            <h2><span class="counter">45</span><span>+</span></h2>
                                            <p>Years of Experience</p>
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-4 col-sm-4">
                                        <div class="counterBox">
                                            <h2><span class="counter">105</span></h2>
                                            <p>Properties</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    <?php
        endwhile;
    }
    ?>
    <div id="tenantLogos" class="container-fluid py-5 homeTenantLogosSec">
        <div class="container-lg centered">
            <div class="row">
                <div class="col-12">
                    <?php $ids = get_post_meta($post->ID, 'vdw_gallery_id', true); ?>
                    <div class="homeAddSec3Carousel owl-carousel owl-carousel-logos">
                        <?php
                        if ($ids) {
                            foreach ($ids as $value) {
                                $image_src = wp_get_attachment_image_src($value, 'medium');
                        ?>
                                <div class="homeAddSec3Inner">
                                    <img src="<?php echo $image_src[0]; ?>">
                                </div>
                        <?php
                            }
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
$args = query_posts(
    array(
        'post_type' => 'services', // This is the name of your CPT
        'order' => 'ASC',
        'posts_per_page' => -1
    )
);
if (have_posts()) {
    while (have_posts()) : the_post();
        $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
?>
        <!-- Bootstrap Modal -->
        <div class="modal fade serviceModal" id="serviceModal<?= get_the_ID() ?>" tabindex="-1" aria-labelledby="serviceModalLabel<?= get_the_ID() ?>" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="container-fluid modalHeaderContainer">
                        <div class="container-lg">
                            <div class="row">
                                <div class="col-12">
                                    <div class="modal-header">
                                        <h4 class="modal-title" id="serviceModalLabel<?= get_the_ID() ?>"><?= get_the_title() ?></h4>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid modalBodyContainer">
                        <div class="container-lg">
                            <div class="row pt-5 pb-4">
                                <?php if(has_post_thumbnail()) { ?>
                                    <div class="col-lg-6 col-12">
                                        <?php the_post_thumbnail('single-post-thumbnail', array('class' => 'img-fluid')); ?>
                                    </div>
                                    <div class="col-lg-6 col-12">
                                        <div class="bulletText modal-body" id="serviceModalContent<?= get_the_ID() ?>">
                                            <?php the_content(); ?>
                                        </div>
                                    </div>
                                <?php } else { ?>
                                <div class="col-12">
                                    <div class="bulletText modal-body" id="serviceModalContent<?= get_the_ID() ?>">
                                        <?php the_content(); ?>
                                    </div>
                                </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
<?php
    endwhile;
}
?>
<section id="services" class="idScrollFix">
    <div class="pt-10 servicesSec">
        <div class="container-xl">
            <div class="row ">
                <div class="col-lg-12">
                    <div class="sectionTitle">
                        <h2>Our <strong>Services</strong></h2>
                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor<br>
                            incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam,<br>
                            quis nostrud exercitation ullamco laboris nisi ut</p>
                    </div>
                </div>
            </div>
            <div class="row ">
                <?php
                if (have_posts()) {
                    while (have_posts()) : the_post();
                        $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
                        $icon = get_field("icon");
                        $shortText = wp_trim_words(get_the_content(), 20);
                ?>

                        <div class="col-lg-3 col-sm-6 col-12">
                            <a href="#" data-bs-toggle="modal" data-bs-target="#serviceModal<?= get_the_ID() ?>" class="servicesBox">
                                <?php if ($icon) { ?>
                                    <img src="<?php echo $icon ?>" alt="">
                                <?php } else { ?>
                                    <img src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/icon.svg" alt="">
                                <?php } ?>
                                <h3><?php the_title(); ?></h3>
                                <?= $shortText ?>
                            </a>
                        </div>

                <?php
                    endwhile;
                }
                wp_reset_query();
                ?>
            </div>
        </div>
    </div>
</section>

<?php
$args = query_posts(array(
    'post_type' => 'listings',
    'order' => 'DESC',
    'posts_per_page' => -1,
    'tax_query' => array(
        array(
            'taxonomy' => 'property-category',
            'field'    => 'slug',
            'terms'    => 'current-listings',
        ),
    ),
));


if (have_posts()) {
    while (have_posts()) : the_post();
        $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
        $city_state = get_field("city_state");
        $price = get_field("price");
        $flyer = get_field("flyer");
?>
        <div class="modal fade listingModal" id="listingModal<?= get_the_ID() ?>" tabindex="-1" aria-labelledby="listingModalLabel<?= get_the_ID() ?>" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="container-fluid modalHeaderContainer">
                        <div class="container-lg">
                            <div class="row">
                                <div class="col-12">
                                    <div class="modal-header">
                                        <h4 class="modal-title" id="listingModalLabel<?= get_the_ID() ?>">Listing Details</h4>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid modal-body py-7 modalBodyContainer">
                        <div class="container-lg">
                            <div class="row">
                                <div class="col-lg-6 col-12">
                                    <?php $ids = get_post_meta($post->ID, 'vdw_gallery_id', true); ?>
                                    <?php if ($ids): ?>
                                        <div class="listingGalleryWrapper">
                                            <!-- Main Carousel -->
                                            <div class="listingMainCarousel owl-carousel">
                                                <?php foreach ($ids as $index => $value):
                                                    $img_full = wp_get_attachment_image_src($value, 'single-post-thumbnail');
                                                    $img_main = wp_get_attachment_image_src($value, [768, 512]);
                                                ?>
                                                    <div class="mainItem">
                                                        <a href="<?php echo esc_url($img_full[0]); ?>" data-fancybox="listing-gallery">
                                                            <img src="<?php echo esc_url($img_main[0]); ?>"
                                                                width="768" height="512"
                                                                class="img-fluid"
                                                                style="aspect-ratio: 3/2; object-fit: cover; object-position: center;" />
                                                        </a>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>

                                            <!-- Thumbnail Carousel -->
                                            <div class="listingThumbCarousel owl-carousel">
                                                <?php foreach ($ids as $index => $value):
                                                    $img_thumb = wp_get_attachment_image_src($value, 'thumbnail');
                                                ?>
                                                    <div class="thumbItem" data-index="<?php echo $index; ?>">
                                                        <img src="<?php echo esc_url($img_thumb[0]); ?>"
                                                            width="100" height="100"
                                                            class="img-fluid"
                                                            style="object-fit: cover; object-position: center;" />
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    <?php else : ?>
                                        <a href="<?php echo esc_url($image[0]); ?>" data-fancybox="listing-gallery">
                                            <img src="<?php echo esc_url($image[0]); ?>"
                                                width="768" height="512"
                                                class="img-fluid"
                                                style="aspect-ratio: 3/2; object-fit: cover; object-position: center;" />
                                        </a>
                                    <?php endif; ?>
                                </div>
                                <div class="col-lg-6 col-12 listingModalDetails">
                                    <div class="d-flex justify-content-between flex-wrap listingInfoBox">
                                        <div>
                                            <h3><?php the_title(); ?></h3>
                                            <?php if ($city_state) { ?>
                                                <h4><?php echo $city_state; ?></h4>
                                            <?php } ?>
                                            <?php if ($price) { ?>
                                                <h5><?php echo $price; ?></h5>
                                            <?php } ?>
                                        </div>
                                        <div class="d-flex align-items-start justify-content-end">
                                            <?php if ($flyer) { ?>
                                                <a href="<?php echo esc_url($flyer); ?>" class="btnPrimary" download>Download Flyer</a>
                                            <?php } ?>
                                        </div>
                                    </div>
                                    <div class="listingContentBox" id="listingModalContent<?= get_the_ID() ?>">
                                        <?php the_content(); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-5">
                                <?php
                                $listing_agents = get_field('listing_agent');
                                if ($listing_agents): ?>
                                    <div class="col-lg-6 col-12">
                                        <div class="row">
                                            <?php foreach ($listing_agents as $listing_agent):
                                                $designation = get_field("designation", $listing_agent->ID);
                                                $first_name = explode(' ', trim(get_the_title($listing_agent->ID)))[0];
                                                $facebook = get_field("facebook", $listing_agent->ID);
                                                $x = get_field("x", $listing_agent->ID);
                                                $linkedin = get_field("linkedin", $listing_agent->ID);
                                                $instagram = get_field("instagram", $listing_agent->ID);
                                            ?>
                                                <div class="col-lg-6 col-12">
                                                    <div class="teamBox">
                                                        <a href="#" data-bs-toggle="modal" data-bs-target="#teamModal<?= $listing_agent->ID ?>" class="teamBoxImg">
                                                            <?php
                                                            if (has_post_thumbnail($listing_agent->ID)):
                                                                echo wp_get_attachment_image(get_post_thumbnail_id($listing_agent->ID), 'team-member-photo', false, array('class' => 'img-fluid'));
                                                            endif;
                                                            ?>
                                                        </a>

                                                        <h4><a href="#" data-bs-toggle="modal" data-bs-target="#teamModal<?= $listing_agent->ID ?>"><?php echo get_the_title($listing_agent->ID); ?></a></h4>
                                                        <span><?php echo $designation; ?></span>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                <div class="col-lg-6 col-12 listingAgentFormBox">
                                    <h3>Contact Us</h3>
                                    <div>
                                        <?php echo do_shortcode('[contact-form-7 id="201b24d" title="Contact Form"]'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
<?php
    endwhile;
}
?>
<section id="listings" class="idScrollFix">
    <div class="container-fluid pt-10 px-0 listingSec">
        <div class="container-xl">
            <div class="row ">
                <div class="col-lg-12">
                    <div class="sectionTitle">
                        <h2>Current <strong>Listings</strong></h2>
                    </div>
                </div>
            </div>
        </div>

        <div class="owl-carousel owl-theme listingInner">
            <?php

            if (have_posts()) {
                while (have_posts()) : the_post();
                    $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
                    $city_state = get_field("city_state");
                    $price = get_field("price");
            ?>
                    <div class="item listingBox" style="margin: 0;">
                        <img src="<?php echo $image[0]; ?>" alt="<?php the_title(); ?>" class="img-fluid" width="768" height="1024"
                            style="aspect-ratio: 768 / 1024; object-fit: cover; object-position: center;">

                        <div class="listingBoxInfo">
                            <h4><?php the_title(); ?></h4>
                            <span><?php echo $city_state; ?></span>
                            <h5><?php echo $price; ?></h5>
                            <a href="#" data-bs-toggle="modal" data-bs-target="#listingModal<?= get_the_ID() ?>">View Details</a>
                        </div>
                    </div>
            <?php
                endwhile;
            }
            wp_reset_query();
            ?>
        </div>
    </div>
</section>
<?php
$args = query_posts(
    array(
        'post_type' => 'team', // This is the name of your CPT
        'order' => 'DESC',
        'posts_per_page' => -1
    )
);
if (have_posts()) {
    while (have_posts()) : the_post();
        $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'team-member-photo');
        $designation = get_field("designation");
        $title = get_the_title();
        $first_name = explode(' ', trim($title))[0];
        $facebook = get_field("facebook");
        $x = get_field("x");
        $linkedin = get_field("linkedin");
        $instagram = get_field("instagram");
?>
        <div class="modal fade teamModal" id="teamModal<?= get_the_ID() ?>" tabindex="-1" aria-labelledby="teamModalLabel<?= get_the_ID() ?>" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="container-fluid modalHeaderContainer">
                        <div class="container-lg">
                            <div class="row">
                                <div class="col-12">
                                    <div class="modal-header">
                                        <h4 class="modal-title" id="teamModalLabel<?= get_the_ID() ?>">About <?= $first_name; ?></h4>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid modal-body py-7 modalBodyContainer">
                        <div class="container-lg">
                            <div class="row">
                                <div class="col-lg-3 col-12 teamModalImgBox">
                                    <?php
                                    if (has_post_thumbnail()):
                                        the_post_thumbnail('team-member-photo', array('class' => 'img-fluid'));
                                    endif;
                                    ?>
                                </div>
                                <div class="col-lg-9 col-12 teamModalDetails">
                                    <div class="d-flex justify-content-between flex-wrap teamInfoBox">
                                        <div>
                                            <h3><?php the_title(); ?></h3>
                                            <?php if ($designation) { ?>
                                                <h4><?php echo $designation; ?></h4>
                                            <?php } ?>
                                        </div>
                                        <div class="d-flex align-items-start justify-content-end gap-3">
                                            <?php if ($facebook) { ?>
                                                <a href="<?php echo esc_url($facebook); ?>" target="_blank"><i class="fa-brands fa-facebook-f"></i></a>
                                            <?php } ?>
                                            <?php if ($x) { ?>
                                                <a href="<?php echo esc_url($x); ?>" target="_blank"><i class="fa-brands fa-x-twitter"></i></a>
                                            <?php } ?>
                                            <?php if ($linkedin) { ?>
                                                <a href="<?php echo esc_url($linkedin); ?>" target="_blank"><i class="fa-brands fa-linkedin-in"></i></a>
                                            <?php } ?>
                                            <?php if ($instagram) { ?>
                                                <a href="<?php echo esc_url($instagram); ?>" target="_blank"><i class="fa-brands fa-instagram"></i></a>
                                            <?php } ?>
                                        </div>
                                    </div>
                                    <div class="bulletText teamContentBox" id="teamModalContent<?= get_the_ID() ?>">
                                        <?php the_content(); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
<?php
    endwhile;
    wp_reset_query();
}
?>

<section id="leadership" class="idScrollFix">
    <div class="py-10 teamSec">
        <div class="container-xl">
            <div class="row ">
                <div class="col-lg-12">
                    <div class="sectionTitle">
                        <h2>Our <strong>Leadership</strong></h2>
                    </div>
                </div>
            </div>
            <div class="row ">
                <?php
                $args = query_posts(
                    array(
                        'post_type' => 'team', // This is the name of your CPT
                        'order' => 'DESC',
                        'posts_per_page' => -1
                    )
                );
                if (have_posts()) {
                    while (have_posts()) : the_post();
                        $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'team-member-photo');
                        $designation = get_field("designation");
                        $first_name = explode(' ', trim(get_the_title()))[0];
                        $facebook = get_field("facebook");
                        $x = get_field("x");
                        $linkedin = get_field("linkedin");
                        $instagram = get_field("instagram");
                ?>

                        <div class="col-lg-3">
                            <div class="teamBox">
                                <a href="#" data-bs-toggle="modal" data-bs-target="#teamModal<?= get_the_ID() ?>" class="teamBoxImg">
                                    <?php
                                    if (has_post_thumbnail()):
                                        the_post_thumbnail('team-member-photo', array('class' => 'img-fluid'));
                                    endif;
                                    ?>
                                </a>

                                <h4><a href="#" data-bs-toggle="modal" data-bs-target="#teamModal<?= get_the_ID() ?>"><?php the_title(); ?></a></h4>
                                <span><?php echo $designation; ?></span>
                            </div>
                        </div>

                <?php
                    endwhile;
                }
                wp_reset_query();
                ?>
            </div>
        </div>
    </div>
</section>

<?php
$args = query_posts(array(
    'post_type' => 'listings',
    'order' => 'DESC',
    'posts_per_page' => -1,
    'tax_query' => array(
        array(
            'taxonomy' => 'property-category',
            'field'    => 'slug',
            'terms'    => 'transactions',
        ),
    ),
));


if (have_posts()) {
    while (have_posts()) : the_post();
        $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
        $city_state = get_field("city_state");
        $price = get_field("price");
        $flyer = get_field("flyer");
?>
        <div class="modal fade listingModal" id="transactionModal<?= get_the_ID() ?>" tabindex="-1" aria-labelledby="listingModalLabel<?= get_the_ID() ?>" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="container-fluid modalHeaderContainer">
                        <div class="container-lg">
                            <div class="row">
                                <div class="col-12">
                                    <div class="modal-header">
                                        <h4 class="modal-title" id="listingModalLabel<?= get_the_ID() ?>">Listing Details</h4>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid modal-body py-7 modalBodyContainer">
                        <div class="container-lg">
                            <div class="row">
                                <div class="col-lg-6 col-12">
                                    <?php $ids = get_post_meta($post->ID, 'vdw_gallery_id', true); ?>
                                    <?php if ($ids): ?>
                                        <div class="listingGalleryWrapper">
                                            <!-- Main Carousel -->
                                            <div class="listingMainCarousel owl-carousel">
                                                <?php foreach ($ids as $index => $value):
                                                    $img_full = wp_get_attachment_image_src($value, 'single-post-thumbnail');
                                                    $img_main = wp_get_attachment_image_src($value, [768, 512]);
                                                ?>
                                                    <div class="mainItem">
                                                        <a href="<?php echo esc_url($img_full[0]); ?>" data-fancybox="listing-gallery">
                                                            <img src="<?php echo esc_url($img_main[0]); ?>"
                                                                width="768" height="512"
                                                                class="img-fluid"
                                                                style="aspect-ratio: 3/2; object-fit: cover; object-position: center;" />
                                                        </a>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>

                                            <!-- Thumbnail Carousel -->
                                            <div class="listingThumbCarousel owl-carousel">
                                                <?php foreach ($ids as $index => $value):
                                                    $img_thumb = wp_get_attachment_image_src($value, 'thumbnail');
                                                ?>
                                                    <div class="thumbItem" data-index="<?php echo $index; ?>">
                                                        <img src="<?php echo esc_url($img_thumb[0]); ?>"
                                                            width="100" height="100"
                                                            class="img-fluid"
                                                            style="object-fit: cover; object-position: center;" />
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    <?php else : ?>
                                        <a href="<?php echo esc_url($image[0]); ?>" data-fancybox="listing-gallery">
                                            <img src="<?php echo esc_url($image[0]); ?>"
                                                width="768" height="512"
                                                class="img-fluid"
                                                style="aspect-ratio: 3/2; object-fit: cover; object-position: center;" />
                                        </a>
                                    <?php endif; ?>
                                </div>
                                <div class="col-lg-6 col-12 listingModalDetails">
                                    <div class="d-flex justify-content-between flex-wrap listingInfoBox">
                                        <div>
                                            <h3><?php the_title(); ?></h3>
                                            <?php if ($city_state) { ?>
                                                <h4><?php echo $city_state; ?></h4>
                                            <?php } ?>
                                            <?php if ($price) { ?>
                                                <h5><?php echo $price; ?></h5>
                                            <?php } ?>
                                        </div>
                                        <div class="d-flex align-items-start justify-content-end">
                                            <?php if ($flyer) { ?>
                                                <a href="<?php echo esc_url($flyer); ?>" class="btnPrimary" download>Download Flyer</a>
                                            <?php } ?>
                                        </div>
                                    </div>
                                    <div class="listingContentBox" id="listingModalContent<?= get_the_ID() ?>">
                                        <?php the_content(); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
<?php
    endwhile;
}
?>

<section id="track-record" class="idScrollFix">
    <div class="py-10 transactionSec" style="background: url('<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/bg-transations.webp') center center no-repeat; background-size:cover;">
        <div class="container-xl">
            <div class="row ">
                <div class="col-lg-12">
                    <div class="sectionTitle sectionTitleWhite">
                        <h2>Recent <strong>Transactions</strong></h2>
                    </div>
                </div>
            </div>
            <div class="owl-carousel owl-theme transactionCarousel">
                <?php

                if (have_posts()) {
                    while (have_posts()) : the_post();
                        $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
                ?>
                        <div class="item">
                            <div class="transactionBox">
                                <div class="transactionBoxImg">
                                    <a href="#" data-bs-toggle="modal" data-bs-target="#transactionModal<?= get_the_ID() ?>">
                                        <img src="<?php echo $image[0] ?>" alt="" class="img-fluid"
                                            style="aspect-ratio: 1 / 1; object-fit: cover; object-position: center;"
                                            width="500" height="500">
                                    </a>
                                </div>

                                <h4><?php the_title(); ?></h4>
                                <a href="#" data-bs-toggle="modal" data-bs-target="#transactionModal<?= get_the_ID() ?>">View Details</a>
                            </div>
                        </div>
                <?php
                    endwhile;
                }
                wp_reset_query();
                ?>
            </div>

        </div>
    </div>
</section>

<?php
$args = query_posts(
    array(
        'post_type' => '', // This is the name of your CPT
        'order' => 'DESC',
        'posts_per_page' => -1
    )
);


if (have_posts()) {
    while (have_posts()) : the_post();
        $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
?>
        <div class="modal fade blogModal" id="blogModal<?= get_the_ID() ?>" tabindex="-1" aria-labelledby="blogModalLabel<?= get_the_ID() ?>" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="container-fluid modalHeaderContainer">
                        <div class="container-lg">
                            <div class="row">
                                <div class="col-12">
                                    <div class="modal-header">
                                        <!-- <h4 class="modal-title" id="blogModalLabel<?= get_the_ID() ?>">Blog</h4> -->
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid modal-body pt-0 pb-7 modalBodyContainer">
                        <div class="container-lg">
                            <div class="row g-5">
                                <div class="col-12 blogModalDetails">
                                    <?php if ($image) { ?>
                                        <div class="blogModalImg">
                                            <img src="<?php echo $image[0] ?>" alt="" class="w-100 img-fluid"
                                                style="aspect-ratio: 4/1; object-fit: cover; object-position: center;"
                                                width="1024" height="256">
                                        </div>
                                    <?php } ?>
                                    <div class="my-4 blogTitleBox">
                                        <h3><?php the_title(); ?></h3>
                                        <h6><?php echo get_the_date('F j, Y'); ?></h6>
                                    </div>
                                    <div class="bulletText blogContentBox" id="blogModalContent<?= get_the_ID() ?>">
                                        <?php the_content(); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
<?php
    endwhile;
}
?>
<section id="blog" class="idScrollFix">
    <div class="py-10 whatHappenSec">
        <div class="container-xl">
            <div class="row ">
                <div class="col-12">
                    <div class="sectionTitle">
                        <h2>What's <strong>Happening</strong></h2>
                    </div>
                </div>
            </div>
            <div class="row g-0 articleBoxesContainer">
                <?php

                if (have_posts()) {
                    while (have_posts()) : the_post();
                        $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
                        $shortText = wp_trim_words(get_the_content(), 16);
                ?>

                        <div class="col-lg-4 col-md-6 col-12 whatHappenBox">
                            <div class="">
                                <h4><?php the_title(); ?></h4>
                                <p><?= $shortText; ?></p>

                                <a href="#" data-bs-toggle="modal" data-bs-target="#blogModal<?= get_the_ID() ?>"><i class="fa-solid fa-arrow-right"></i> Read more</a>
                            </div>
                        </div>

                <?php
                    endwhile;
                }
                wp_reset_query();
                ?>
            </div>
        </div>
    </div>
</section>



<section id="testimonials" class="idScrollFix">
    <div class="py-10 testimonialSec">
        <div class="container-xl">
            <div class="row">
                <div class="col-lg-12">
                    <div class="sectionTitle">
                        <h2>What Our Clients <strong>Are Saying</strong></h2>
                    </div>
                </div>
            </div>
            <div class="row position-relative justify-content-end">
                <div class="col-lg-5 testimonialImg" style="background: url('<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/testimonials.webp') left center no-repeat; background-size: cover;"></div>
                <div class="col-lg-9">
                    <div class="testimonialSlider owl-carousel owl-theme">
                        <?php
                        $args = query_posts(
                            array(
                                'post_type' => 'testimonial', // This is the name of your CPT
                                'order' => 'ASC',
                                'posts_per_page' => -1
                            )
                        );
                        if (have_posts()) {
                            while (have_posts()) : the_post();
                                $image = wp_get_attachment_image_src(get_post_thumbnail_id($post->ID), 'single-post-thumbnail');
                                $citystate = get_field("address");
                                $shortText = wp_trim_words(get_the_excerpt(), 12);
                        ?>
                                <div class="testimonialBox">
                                    <h3><?php the_title(); ?></h3>
                                    <?php the_content(); ?>
                                    <div class="testimonialBoxImg">
                                        <?php if ($image[0]) { ?>
                                            <img src="<?php echo $image[0] ?>" alt="" class="img-fluid">
                                        <?php } ?>
                                    </div>
                                </div>
                        <?php
                            endwhile;
                        }
                        wp_reset_query();
                        ?>
                    </div>
                </div>
                <div id="testimonialProgressBar" class="col-lg-7">
                    <div class="testimonialBottom">
                        <div class="top">
                            <h3>Happy Clients</h3>
                            <h3>100%</h3>
                        </div>
                        <div class="bottom">
                            <h3>Lorem Ipsum</h3>
                            <h3>100%</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>






<section id="contact" class="idScrollFix">
    <div class="container-fluid contactSec">
        <div class="row">
            <div class="col-lg-6 p-12 contactSecForm" style="background: url('<?php echo esc_url(get_template_directory_uri()); ?>/assets/img/bg-contact.webp') center center no-repeat; background-size:cover;">
                <div class="contactSecFormInner">
                    <?php echo $home_meta['contact_text']['value']; ?>

                    <?php echo do_shortcode('[contact-form-7 id="201b24d" title="Contact Form"]'); ?>
                </div>
            </div>

            <div class="col-lg-6 contactSecInfo" id="map">

                <div class="contactSecInfoInner">
                    <ul>
                        <?php if ($home_meta['phone']['value']) { ?>
                            <li>
                                <div class="icon">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <div class="text">
                                    <h4>Phone</h4>
                                    <a href="tel:<?php echo $home_meta['phone']['value']; ?>"><?php echo $home_meta['phone']['value']; ?></a>
                                </div>
                            </li>
                        <?php }
                        if ($home_meta['email']['value']) { ?>
                            <li>
                                <div class="icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="text">
                                    <h4>Email</h4>
                                    <a href="mailto:<?php echo $home_meta['email']['value']; ?>"><?php echo $home_meta['email']['value']; ?></a>
                                </div>
                            </li>
                        <?php }
                        if ($home_meta['address']['value']) { ?>
                            <li>
                                <div class="icon">
                                    <i class="fas fa-map"></i>
                                </div>
                                <div class="text">
                                    <h4>Address</h4>
                                    <?php echo $home_meta['address']['value']; ?>
                                </div>
                            </li>
                        <?php } ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>



<?php get_footer(); ?>
<script>
    // JavaScript to handle the active state
    document.addEventListener('DOMContentLoaded', function() {
        const sections = document.querySelectorAll('section');
        const mainMenu = document.querySelector('#menu-main-menu'); // Only this menu
        const navLinks = mainMenu ? mainMenu.querySelectorAll('a[href^="#"]') : [];

        // Function to activate the nav item
        // Detailed explanation of what it does:
        // 1. Loop through each nav link
        // 2. If the link's href matches the id of the section, add the activeNavItem class to the parent li
        // 3. If the link's href does not match the id of the section, remove the activeNavItem class from the parent li
        const activateNavItem = (id) => {
            navLinks.forEach(link => {
                const parentLi = link.closest('li');
                if (!parentLi) return;

                if (link.getAttribute('href') === `#${id}`) {
                    parentLi.classList.add('activeNavItem');
                } else {
                    parentLi.classList.remove('activeNavItem');
                }
            });
        };

        // Intersection Observer
        // Detailed explanation of what it does:
        // 1. Create an observer with options
        // 2. When the observer detects that a section is in the viewport (rootMargin: '0px 0px -60% 0px' means it triggers when the top 40% of the section is in the viewport), it calls the callback function
        // 3. The callback function checks if the section has an id
        // 4. If it does, it calls the activateNavItem function with the id as the argument
        const observerOptions = {
            root: null,
            rootMargin: '0px 0px -60% 0px', // top 40%
            threshold: 0
        };

        // Create an observer
        // Detailed explanation of what it does:
        // 1. Create an observer with options
        // 2. When the observer detects that a section is in the viewport (rootMargin: '0px 0px -60% 0px' means it triggers when the top 40% of the section is in the viewport), it calls the callback function
        // 3. The callback function checks if the section has an id
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && entry.target.id) {
                    activateNavItem(entry.target.id);
                }
            });
        }, observerOptions);

        // Observe each section
        // Detailed explanation of what it does:
        // 1. Loop through each section
        // 2. If the section has an id, observe it
        sections.forEach(section => {
            if (section.id) observer.observe(section);
        });
    });
</script>
<script>
    // Color Pickers for Primary & Secondary
    const primaryColorPicker = document.getElementById("primaryColorPicker");
    const secondaryColorPicker = document.getElementById("secondaryColorPicker");
    const teamOverlayColorPicker = document.getElementById("teamOverlayColorPicker");


    primaryColorPicker.addEventListener("input", (event) => {
        const newColor = event.target.value;
        document.documentElement.style.setProperty('--primary', newColor);
    });

    secondaryColorPicker.addEventListener("input", (event) => {
        const newColor = event.target.value;
        document.documentElement.style.setProperty('--secondary', newColor);
    });

    // Overlay Color Picker → Set --overlay CSS variable
    teamOverlayColorPicker.addEventListener("input", (event) => {
        const newColor = event.target.value;
        document.documentElement.style.setProperty('--overlay', newColor);
    });

    // Headshot Style Toggle
    const headshotsRadios = document.querySelectorAll('input[name="headshot_style"]');

    // Apply initial style based on checked radio
    const checkedHeadshot = document.querySelector('input[name="headshot_style"]:checked');
    if(checkedHeadshot) {
        document.querySelectorAll('.teamBox').forEach(el => {
            if(checkedHeadshot.value === 'Colored') {
                el.style.filter = 'none';
            } else {
                el.style.filter = 'grayscale(100%)';
            }
        });
    }
    
    headshotsRadios.forEach(radio => {
        radio.addEventListener('change', (e) => {
            document.querySelectorAll('.teamBox').forEach(el => {
                if(e.target.value === 'Colored') {
                    el.style.filter = 'none';
                } else {
                    el.style.filter = 'grayscale(100%)';
                }
            });
        });
    });

    // Sections Toggle
    function customToggleSection(sectionId, radioName) {
        const section = document.getElementById(sectionId);
        const onRadio = document.querySelector(`input[name="${radioName}"][value="on"]`);
        const offRadio = document.querySelector(`input[name="${radioName}"][value="off"]`);

        function updateDisplay() {
            section.style.display = onRadio.checked ? "block" : "none";
        }

        // Set initial state
        updateDisplay();

        // Listen for changes
        onRadio.addEventListener("change", updateDisplay);
        offRadio.addEventListener("change", updateDisplay);
    }

    // Call for each section with matching radioName and sectionId
    customToggleSection("about", "aboutSection");
    customToggleSection("scrollingNumbers", "scrollingNumbers");
    customToggleSection("tenantLogos", "clientLogos");
    customToggleSection("services", "servicesSection");
    customToggleSection("listings", "listingsSection");
    customToggleSection("leadership", "leadershipSection");
    customToggleSection("track-record", "transactionsSection");
    customToggleSection("blog", "whatsHappeningSection");
    customToggleSection("testimonials", "testimonialsSection");
    customToggleSection("testimonialProgressBar", "testimonialsProgressBar");
    customToggleSection("newsletterSection", "newslettersSection");
</script>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        const originalBoxes = document.querySelectorAll(".customizeTemplateBtnBox");
        const fixedBox = document.querySelector(".customizeBtnBoxFixed");

        if (!originalBoxes.length || !fixedBox) return;

        let isAnyVisible = false;

        const observer = new IntersectionObserver((entries) => {
            // Check if any of the original buttons is visible
            isAnyVisible = entries.some(entry => entry.isIntersecting);

            if (isAnyVisible) {
                fixedBox.style.display = "none";
            } else {
                fixedBox.style.display = "block";
            }
        }, {
            root: null,
            threshold: 0.1,
        });

        originalBoxes.forEach(box => observer.observe(box));
    });
</script>


<script src="https://code.jquery.com/jquery-2.2.4.js" integrity="sha256-iT6Q9iMJYuQiMWNd9lDyBUStIq/8PuOW33aOqmvFpqI=" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/waypoints/2.0.3/waypoints.min.js"></script>
<script src="<?php echo esc_url(get_template_directory_uri()); ?>/assets/js/counter.js"></script>
<script>
    jQuery(document).ready(function($) {
        $('.counter').counterUp({
            delay: 10, // the delay time in ms
            time: 3000 // the speed time in ms
        });
    });
</script>

<!--Leaflet-->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" integrity="sha512-xodZBNTC5n17Xt2atTPuE1HxjVMSvLVW9ocqUKLsCC5CXdbqCmblAshOMAS6/keqq/sMZMZ19scR4PsZChSR7A==" crossorigin="" />
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js" integrity="sha512-XQoYMqMTK8LvdxXYG3nZ448hOEQiglfqkJs1NOQV44cWnUrBc8PkAOcXy20w0vlaXaVUearIOBhiXZ5V3ynxwA==" crossorigin=""></script>

<script>
    var locations = [
        ["<h3><?php echo $home_meta['map_address']['value']; ?></h3>", <?php echo $home_meta['latlng']['value']; ?>]
    ];

    var LeafIcon = L.Icon.extend({
        options: {
            iconSize: [46, 54],
            iconAnchor: [23, 54],
            shadowAnchor: [4, 62],
            popupAnchor: [0, -50]
        }
    });

    var greenIcon = new LeafIcon({
        iconUrl: '<?php bloginfo('url'); ?>/wp-content/uploads/2025/05/mappin.png',
    })

    var map = L.map('map').setView([<?php echo $home_meta['latlng']['value']; ?>], 10);
    map.attributionControl.setPrefix('');
    map.scrollWheelZoom.disable();
    map.doubleClickZoom.disable();

    mapLink = '<a href="http://focusedcre.com/">FocusedCRE</a>';

    googleStreets = L.tileLayer('http://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}', {
        maxZoom: 12,
        attribution: '&copy; ' + mapLink + ' Contributors',
        subdomains: ['mt0', 'mt1', 'mt2', 'mt3']
    }).addTo(map);

    var markers = [];
    var bounds = L.latLngBounds();

    for (var i = 0; i < locations.length; i++) {
        markers.push([locations[i][1], locations[i][2]]);
        var marker = new L.marker([locations[i][1], locations[i][2]], {
                icon: greenIcon
            })
            .bindPopup(locations[i][0])
            .addTo(map);
        bounds.extend([locations[i][1], locations[i][2]]);
    }
    map.fitBounds(bounds, {
        padding: [50, 50]
    });
</script>


<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui/dist/fancybox.umd.js"></script>

<script>
    $(document).ready(function() {
        // Apply default classes on page load
        $('body').addClass('lightTheme bannerOption1');

        $('#bannerOption1').addClass('activeBannerOptionBtn');

        // Theme button click
        $('input[name="theme"]').on('change', function() {
            $('body').removeClass('lightTheme darkTheme');
            
            if (this.value === 'light') {
                $('body').addClass('lightTheme');
            } else if (this.value === 'dark') {
                $('body').addClass('darkTheme');
            }
        });

        // Banner radio button change
        $('input[name="main_banner"]').on('change', function() {
            $('body').removeClass('bannerOption1 bannerOption2 bannerOption3');
            $('body').addClass(this.id);
        });
    });



    $(document).ready(function() {

        $(".bannerImageSlider").owlCarousel({
            items: 1,
            loop: true,
            autoplay: true,
            autoplayTimeout: 5000, // 5 seconds
            autoplayHoverPause: true,
            animateOut: 'fadeOut',
            animateIn: 'fadeIn',
            nav: false,
            dots: false,
            smartSpeed: 800
        });

        $('.listingInner').owlCarousel({
            loop: true,
            margin: 0,
            nav: true,
            dots: false,
            autoplay: true,
            autoplayTimeout: 6000,
            // pauseOnHover: false,
            navText: [
                '<span class="fa-solid fa-chevron-left"></span>',
                '<span class="fa-solid fa-chevron-right"></span>',
            ],
            responsive: {
                0: {
                    items: 1
                },
                576: {
                    items: 2
                },
                768: {
                    items: 3
                },
                992: {
                    items: 4
                }
            }
        });

        const mainCarousel = $('.listingMainCarousel');
        const thumbCarousel = $('.listingThumbCarousel');

        mainCarousel.owlCarousel({
            items: 1,
            nav: false,
            dots: false,
            loop: true,
            margin: 10,
            autoplay: true,
            autoplayTimeout: 3000,
            autoplayHoverPause: true,
            smartSpeed: 600
        }).on('changed.owl.carousel', function(event) {
            const index = event.item.index;
            const count = event.item.count;

            // Owl Carousel clones items before and after. To get the real index:
            const realIndex = event.relatedTarget.relative(index);

            // Add active class to thumbnail
            thumbCarousel.find('.thumbItem').removeClass('active');
            thumbCarousel.find(`.thumbItem[data-index="${realIndex}"]`).addClass('active');

            // Scroll thumbnail carousel to match
            thumbCarousel.trigger('to.owl.carousel', [realIndex, 300, true]);
        });

        thumbCarousel.owlCarousel({
            items: 6,
            margin: 5,
            nav: false,
            dots: false,
            responsive: {
                0: {
                    items: 1
                },
                576: {
                    items: 3
                },
                768: {
                    items: 4
                },
                992: {
                    items: 5
                }
            }
        });

        // Thumbnail click -> go to that slide
        thumbCarousel.on('click', '.thumbItem', function() {
            const index = $(this).data('index');
            mainCarousel.trigger('to.owl.carousel', [index, 300, true]);
        });

        // Initialize Fancybox
        Fancybox.bind('[data-fancybox="listing-gallery"]', {
            Thumbs: false,
            Toolbar: {
                display: ['close']
            }
        });

        // Transaction Images Toggle

        const transactionImagesRadios = document.querySelectorAll('input[name="transaction_image_style"]');
        const transactionImageHoverRadios = document.querySelectorAll('input[name="transaction_image_hover_style"]');

        function applyTransactionStyles() {
            const transactionBoxes = document.querySelectorAll('.transactionBox');
            const selectedStyle = document.querySelector('input[name="transaction_image_style"]:checked').value;
            const selectedHoverStyle = document.querySelector('input[name="transaction_image_hover_style"]:checked').value;

            transactionBoxes.forEach(el => {
                el.classList.remove('bwTransactionImage', 'coloredTransactionImage', 'transactionImageHoverColored', 'transactionImageHoverBW');

                if (selectedStyle === 'Black & White') {
                    el.classList.add('bwTransactionImage');
                } else {
                    el.classList.add('coloredTransactionImage');
                }

                if (selectedHoverStyle === 'Black & White') {
                    el.classList.add('transactionImageHoverBW');
                } else {
                    el.classList.add('transactionImageHoverColored');
                }
            });
        }

        // Add event listeners to radio buttons
        transactionImagesRadios.forEach(radio => {
            radio.addEventListener('change', applyTransactionStyles);
        });
        transactionImageHoverRadios.forEach(radio => {
            radio.addEventListener('change', applyTransactionStyles);
        });
      
        // ✅ Apply styles on page load
        applyTransactionStyles();

        // ✅ Owl Carousel Init
        $('.transactionCarousel').owlCarousel({
            loop: true,
            margin: 30,
            nav: true,
            dots: false,
            autoplay: true,
            autoplayTimeout: 6000,
            navText: [
                '<span class="fa-solid fa-chevron-left"></span>',
                '<span class="fa-solid fa-chevron-right"></span>',
            ],
            responsive: {
                0: {
                    items: 1
                },
                576: {
                    items: 2
                },
                768: {
                    items: 3
                },
                992: {
                    items: 3
                }
            },
            onInitialized: applyTransactionStyles,
            onRefreshed: applyTransactionStyles
        });

        // ✅ Button Events
        transactionImagesColoredBtn.addEventListener('click', () => {
            transactionImagesColoredBtn.classList.add('activeTransactionStyleBtn');
            transactionImagesBWBtn.classList.remove('activeTransactionStyleBtn');
            applyTransactionStyles();
        });

        transactionImagesBWBtn.addEventListener('click', () => {
            transactionImagesBWBtn.classList.add('activeTransactionStyleBtn');
            transactionImagesColoredBtn.classList.remove('activeTransactionStyleBtn');
            applyTransactionStyles();
        });

        transactionImageHoverColoredBtn.addEventListener('click', () => {
            transactionImageHoverColoredBtn.classList.add('activeTransactionHoverStyleBtn');
            transactionImageHoverBWBtn.classList.remove('activeTransactionHoverStyleBtn');
            applyTransactionStyles();
        });

        transactionImageHoverBWBtn.addEventListener('click', () => {
            transactionImageHoverBWBtn.classList.add('activeTransactionHoverStyleBtn');
            transactionImageHoverColoredBtn.classList.remove('activeTransactionHoverStyleBtn');
            applyTransactionStyles();
        });


        $(".memberBenefitsSlider").owlCarousel({
            loop: true,
            margin: 30,
            nav: true,
            dots: false,
            items: 3,
            autoplay: true,
            autoplayTimeout: 6000,
            navText: [
                '<span class="fa-solid fa-chevron-left"></span>',
                '<span class="fa-solid fa-chevron-right"></span>',
            ],
            responsive: {
                0: {
                    items: 1
                },
                767: {
                    items: 2
                },
                991: {
                    items: 3
                }
            },
        });

        $(".meetTeamSlider").owlCarousel({
            loop: true,
            margin: 0,
            nav: false,
            dots: true,
            items: 1,
            autoplayTimeout: 3000,
            autoplay: true,
            navText: [
                '<span class="fa-solid fa-arrow-right-long left"></span>',
                '<span class="fa-solid fa-arrow-right-long"></span>',
            ],
            responsive: {
                0: {
                    items: 1
                },
                991: {
                    items: 1
                }
            },
        });

        $(".homeAboutSlider").owlCarousel({
            loop: true,
            margin: 0,
            nav: false,
            dots: false,
            items: 1,
            autoplayTimeout: 3000,
            autoplay: true,
            navText: [
                '<span class="fa-solid fa-arrow-right-long left"></span>',
                '<span class="fa-solid fa-arrow-right-long"></span>',
            ],
            responsive: {
                0: {
                    items: 1
                },
                991: {
                    items: 1
                }
            },
        });

    });
</script>



<script>
    document.addEventListener("DOMContentLoaded", function() {
        const topSection = document.querySelector(".testimonialBottom .top");
        const bottomSection = document.querySelector(".testimonialBottom .bottom");
        const topCounter = topSection.querySelector("h3:last-child");
        const bottomCounter = bottomSection.querySelector("h3:last-child");

        function animateCounter(element, endValue, duration) {
            let start = 0;
            const increment = (endValue - start) / (duration / 20);

            const interval = setInterval(() => {
                start += increment;
                if (start >= endValue) {
                    start = endValue;
                    clearInterval(interval);
                }
                element.textContent = Math.floor(start) + "%";
            }, 20);
        }

        const observer = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    if (entry.target === topSection) {
                        topSection.classList.add("active");
                        animateCounter(topCounter, 100, 2000);
                    }

                    if (entry.target === bottomSection) {
                        bottomSection.classList.add("active"); // Even though no :after transition, for consistency
                        animateCounter(bottomCounter, 100, 2000);
                    }

                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.5
        });

        observer.observe(topSection);
        observer.observe(bottomSection);
    });
</script>


<script>
    $(document).ready(function() {
        $('.owl-carousel-logos').owlCarousel({
            loop: true,
            nav: false,
            dots: false,
            navText: [
                '<i class="fa-solid fa-chevron-left"></i>',
                '<i class="fa-solid fa-chevron-right"></i>',
            ],
            autoplay: true,
            autoplayTimeout: 4000,
            slideTransition: 'linear',
            autoplaySpeed: 4000,
            smartSpeed: 4000,
            center: true,
            responsiveClass: true,
            margin: 20,
            // 	center:true,
            autoplayHoverPause: true,
            items: 3.05, // Number of items to show
            responsive: {
                0: {
                    items: 2
                },
                768: {
                    items: 3
                },
                992: {
                    items: 4.05
                }
            }
        });

        jQuery('.owl-carousel-logos').trigger('play.owl.autoplay', [2000]);

        function setSpeed() {
            jQuery('.owl-carousel-logos').trigger('play.owl.autoplay', [4000]);
        }

        setTimeout(setSpeed, 1000);

        $(".testimonialSlider").owlCarousel({
            loop: true,
            margin: 20,
            nav: false,
            dots: false,
            items: 3,
            autoplayTimeout: 3000,
            autoplay: true,
            navText: [
                '<span class="fa-solid fa-arrow-right-long left"></span>',
                '<span class="fa-solid fa-arrow-right-long"></span>',
            ],
            responsive: {
                0: {
                    items: 1
                },
                991: {
                    items: 2
                }
            },
        });
    });
</script>