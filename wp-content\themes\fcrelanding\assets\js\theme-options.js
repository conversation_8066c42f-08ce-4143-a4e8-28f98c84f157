/**
 * Theme Options JavaScript
 * Handles dynamic application of theme options on the front-end
 */

document.addEventListener('DOMContentLoaded', function() {

    // Add class to body to indicate theme options are active
    document.body.classList.add('fcre-theme-options-active');

    // Get theme options from localized script
    if (typeof fcre_theme_options !== 'undefined') {
        applyThemeOptions(fcre_theme_options);

        // Debug log for development
        if (window.console && console.log) {
            console.log('FCRE Theme Options Applied:', fcre_theme_options);
        }
    } else {
        console.warn('FCRE Theme Options not found. Using default settings.');
    }
    
    /**
     * Apply theme options to the page
     */
    function applyThemeOptions(options) {
        // Apply theme class
        if (options.theme === 'dark') {
            document.body.classList.add('darkTheme');
        } else {
            document.body.classList.remove('darkTheme');
        }
        
        // Apply banner option classes
        document.body.classList.remove('bannerOption1', 'bannerOption2', 'bannerOption3');
        const bannerClass = 'bannerOption' + options.main_banner.replace('Option ', '');
        document.body.classList.add(bannerClass);
        
        // Apply headshot style classes
        const teamBoxes = document.querySelectorAll('.teamBox');
        teamBoxes.forEach(function(box) {
            if (options.headshot_style === 'Colored') {
                box.classList.remove('bwHeadshot');
                box.classList.add('coloredHeadshot');
            } else {
                box.classList.remove('coloredHeadshot');
                box.classList.add('bwHeadshot');
            }
        });
        
        // Apply transaction image styles
        const transactionBoxes = document.querySelectorAll('.transactionBox');
        transactionBoxes.forEach(function(box) {
            if (options.transaction_image_style === 'Colored') {
                box.classList.remove('bwTransaction');
                box.classList.add('coloredTransaction');
            } else {
                box.classList.remove('coloredTransaction');
                box.classList.add('bwTransaction');
            }
            
            // Apply hover styles
            if (options.transaction_image_hover_style === 'Colored') {
                box.classList.remove('bwTransactionHover');
                box.classList.add('coloredTransactionHover');
            } else {
                box.classList.remove('coloredTransactionHover');
                box.classList.add('bwTransactionHover');
            }
        });
        
        // Apply section visibility
        const sectionMap = {
            'aboutSection': '#about',
            'scrollingNumbers': '#scrollingNumbers',
            'clientLogos': '#tenantLogos',
            'servicesSection': '#services',
            'listingsSection': '#listings',
            'leadershipSection': '#leadership',
            'transactionsSection': '#transactions',
            'whatsHappeningSection': '#whatsHappening',
            'testimonialsSection': '#testimonials',
            'testimonialsProgressBar': '.testimonials-progress-bar',
            'newslettersSection': '#newsletters'
        };
        
        Object.keys(sectionMap).forEach(function(optionKey) {
            const selector = sectionMap[optionKey];
            const elements = document.querySelectorAll(selector);
            
            elements.forEach(function(element) {
                if (options[optionKey] === 'off') {
                    element.style.display = 'none';
                } else {
                    element.style.display = '';
                }
            });
        });
        
        // Update CSS custom properties
        updateCSSCustomProperties(options);
    }
    
    /**
     * Update CSS custom properties for colors
     */
    function updateCSSCustomProperties(options) {
        const root = document.documentElement;
        
        if (options.primary_color) {
            root.style.setProperty('--primary', options.primary_color);
        }
        
        if (options.secondary_color) {
            root.style.setProperty('--secondary', options.secondary_color);
        }
        
        if (options.team_overlay_color) {
            root.style.setProperty('--overlay', options.team_overlay_color);
        }
        
        // Update primaryColor and secondaryColor based on theme
        if (options.theme === 'dark') {
            root.style.setProperty('--primaryColor', 'var(--secondary)');
            root.style.setProperty('--secondaryColor', 'var(--primary)');
        } else {
            root.style.setProperty('--primaryColor', 'var(--primary)');
            root.style.setProperty('--secondaryColor', 'var(--secondary)');
        }
    }
    
    /**
     * Handle banner option display
     */
    function handleBannerDisplay(bannerOption) {
        const imageSlider = document.querySelector('.bannerImageSliderSec');
        const videoSection = document.querySelector('.homeVideoSec');
        const vectorOverlay = document.querySelector('.homeBannerVectorOverlay');
        
        if (bannerOption === 'Option 1') {
            // Background video with shapes and image
            if (imageSlider) imageSlider.style.display = 'none';
            if (videoSection) videoSection.style.display = 'block';
            if (vectorOverlay) vectorOverlay.style.display = 'block';
        } else if (bannerOption === 'Option 2') {
            // Only background video
            if (imageSlider) imageSlider.style.display = 'none';
            if (videoSection) videoSection.style.display = 'block';
            if (vectorOverlay) vectorOverlay.style.display = 'none';
        } else if (bannerOption === 'Option 3') {
            // Image slider
            if (imageSlider) imageSlider.style.display = 'block';
            if (videoSection) videoSection.style.display = 'none';
            if (vectorOverlay) vectorOverlay.style.display = 'none';
        }
    }
    
    // Apply banner display on load
    if (typeof fcre_theme_options !== 'undefined' && fcre_theme_options.main_banner) {
        handleBannerDisplay(fcre_theme_options.main_banner);
    }
    
});

/**
 * Utility function to get theme option value
 */
function fcreGetThemeOption(optionName, defaultValue = '') {
    if (typeof fcre_theme_options !== 'undefined' && fcre_theme_options[optionName]) {
        return fcre_theme_options[optionName];
    }
    return defaultValue;
}
